<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأخبار</title>
    <!-- إخفاء رأس وتذييل المتصفح عند الطباعة -->
    <style>
        @media print {
            @page { margin: 0; }
            body { margin: 1cm; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
    <style>
        @page {
            size: A4;
            margin: 1cm;
            orphans: 2; /* منع ظهور أقل من 2 أسطر في بداية الصفحة */
            widows: 2; /* منع ظهور أقل من 2 أسطر في نهاية الصفحة */
            /* إخفاء رأس وتذييل المتصفح */
            margin-header: 0;
            margin-footer: 0;
        }

        /* إخفاء رأس وتذييل المتصفح في جميع المتصفحات */
        @media print {
            @page {
                margin: 1cm;
                size: A4;
            }
            html {
                margin: 0 !important;
                padding: 0 !important;
            }
        }

        @font-face {
            font-family: 'Tajawal';
            font-display: swap;
            font-weight: normal;
            font-style: normal;
        }

        body {
            font-family: 'Tajawal', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            direction: rtl;
            line-height: 1.6;
            background-color: #e6f2ff;
        }

        html {
            background-color: #e6f2ff;
            min-height: 100%;
        }

        .header {
            background-color: #0056b3;
            color: white;
            text-align: center;
            padding: 15px 0;
            margin-bottom: 15px;
            position: relative;
            width: 100%;
        }

        .date-top-right {
            position: absolute;
            top: 15px;
            right: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }



        .logo {
            width: 120px;
            height: auto;
            margin: 0 auto 10px;
            display: block;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0 5px;
        }

        .subtitle {
            font-size: 16px;
            margin: 3px 0;
            font-weight: 500;
        }

        .department {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 4px 15px;
            display: inline-block;
            margin: 5px 3px;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 14px;
        }

        .date-bar {
            background-color: #0056b3;
            color: white;
            padding: 8px 15px;
            text-align: left;
            margin: 15px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 500;
            font-size: 14px;
        }

        .news-item {
            border: none;
            margin-bottom: 15px;
            page-break-inside: avoid;
            break-inside: avoid;
            background-color: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-top: 3px solid #0056b3;
            display: block;
            position: relative;
        }

        .news-title {
            background-color: #f8f8f8;
            border-right: 5px solid #c00;
            padding: 8px 15px;
            font-weight: 700;
            font-size: 16px;
            color: #c00;
            border-bottom: 1px solid #eee;
        }

        .news-content {
            padding: 10px 15px;
            line-height: 1.5;
            color: #444;
            font-size: 14px;
            text-align: justify;
        }

        .news-meta {
            background-color: #f8f8f8;
            padding: 5px 15px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }

        .meta-item {
            display: inline-block;
            margin-left: 15px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            padding-bottom: 20px;
            font-size: 12px;
            color: #555;
            border-top: 2px solid #0056b3;
            background-color: #f8f8f8;
        }

        .footer-logo {
            width: 60px;
            height: auto;
            margin-bottom: 10px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .page-number {
            text-align: center;
            margin-top: 15px;
            font-size: 10px;
            color: #999;
            position: fixed;
            bottom: 5px;
            left: 0;
            right: 0;
        }

        .page-number:after {
            content: counter(page);
        }

        .page-break {
            page-break-after: always;
            margin: 0;
            padding: 0;
            height: 0;
        }

        .print-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background-color: #0056b3;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 15px;
            font-family: 'Tajawal', Arial, sans-serif;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .print-button:hover {
            background-color: #003d7a;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 86, 179, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 18px;
        }

        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-left: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media print {
            .print-button {
                display: none;
            }

            /* إعدادات لضمان طباعة الألوان والخلفيات */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* إخفاء رأس وتذييل المتصفح */
            @page {
                size: A4;
                margin: 0;
            }

            /* إضافة هوامش للمحتوى بدلاً من الصفحة */
            body {
                margin: 1cm !important;
                padding: 0 !important;
            }

            body {
                background-color: #e6f2ff !important;
            }

            .header {
                background-color: #0056b3 !important;
                color: white !important;
                margin-bottom: 5mm !important;
            }

            .date-top-right {
                position: absolute !important;
                top: 15px !important;
                right: 20px !important;
                background-color: rgba(255, 255, 255, 0.2) !important;
                padding: 8px 12px !important;
                border-radius: 5px !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
            }

            .news-title {
                color: #c00 !important;
                border-right: 5px solid #c00 !important;
                background-color: #f8f8f8 !important;
                padding: 5px 15px !important;
            }

            .news-content {
                padding: 5px 15px !important;
                margin: 0 !important;
            }

            .news-meta {
                padding: 3px 15px !important;
                margin: 0 !important;
            }

            .news-item {
                border-top: 3px solid #0056b3 !important;
                background-color: white !important;
                margin-bottom: 5mm !important;
                break-inside: avoid !important;
                page-break-inside: avoid !important;
                display: block !important;
            }

            .main-content {
                padding: 5mm !important;
            }

            /* تجنب الفواصل داخل عناصر الأخبار */
            p, h1, h2, h3, h4, h5, h6, article, .news-item, .news-content, .news-meta, .news-title {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* تعديل خصائص الطباعة */
            html, body {
                height: auto !important;
                overflow: visible !important;
            }

            /* تعديل خصائص الصفحة */
            .printing .news-item {
                margin-bottom: 10mm !important;
                page-break-after: auto !important;
            }

            /* تنسيق التذييل عند الطباعة */
            .footer {
                background-color: #f8f8f8 !important;
                border-top: 2px solid #0056b3 !important;
                color: #555 !important;
                padding-top: 15px !important;
                padding-bottom: 20px !important;
            }

            .footer-logo {
                width: 60px !important;
                height: auto !important;
                display: block !important;
                margin: 0 auto 10px !important;
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div>
            جاري تحضير التقرير للطباعة...
            <div class="loading-spinner"></div>
        </div>
    </div>

    <button class="print-button" onclick="printReport()"><i class="fas fa-print"></i> طباعة</button>

    <script>
        // دالة طباعة محسنة
        function printReport() {
            // إظهار مؤشر التحميل
            var loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            // تطبيق أنماط إضافية قبل الطباعة
            var style = document.createElement('style');
            style.innerHTML = `
                @page {
                    margin: 0;
                    size: A4;
                }
                @media print {
                    body { margin: 1cm !important; }
                    .loading-overlay { display: none !important; }
                }
            `;
            document.head.appendChild(style);

            // تأخير مختصر لضمان تحميل العناصر الأساسية
            setTimeout(function() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                window.print();
            }, 150);
        }

        // تحسين تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء مؤشر التحميل عند اكتمال تحميل الصفحة
            var loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }

            // تعيين اسم الملف
            {% if filename %}
            document.title = "{{ filename }}";
            {% endif %}
        });

        // إضافة مستمع حدث للطباعة
        window.addEventListener('beforeprint', function() {
            // تطبيق أنماط إضافية قبل الطباعة
            document.body.classList.add('printing');
        });
    </script>
    <div class="header">
        <div class="date-top-right">
            <i class="fas fa-calendar-alt" style="margin-left: 8px;"></i>{{ today_date }}
        </div>
        <img src="{{ url_for('static', filename='img/ihchr.png', _external=True) }}" alt="شعار المفوضية" class="logo" loading="eager">
        <div class="title">الرصد الإعلامي</div>
        <div class="subtitle">المفوضية العليا لحقوق الإنسان في العراق</div>
        <div class="department">قسم العلاقات والإعلام</div>
        <div class="department">وحدة الرصد الإعلامي</div>
    </div>

    <div class="main-content" style="background-color: rgba(240, 248, 255, 0.7); padding: 10px; border-radius: 8px; margin-top: 0; margin-bottom: 0;">
    {% if news_list %}
        {% for news in news_list %}
        <article class="news-item">
            <div class="news-title">{{ news.title }}</div>
            <div class="news-content">{{ news.content|nl2br|safe }}</div>
            <div class="news-meta">
                <div class="meta-item">التاريخ: {{ news.date|format_date_arabic }}</div>
                <div class="meta-item">المصدر:
                    {% if news.source_url %}
                        <a href="{{ news.source_url }}" target="_blank" style="color: inherit; text-decoration: none;">
                            {{ news.source }}
                            <span style="font-size: 0.8em; color: #0056b3;">&#x2197;</span>
                        </a>
                    {% else %}
                        {{ news.source }}
                    {% endif %}
                </div>
                <div class="meta-item">الموقع: {{ news.governorate.name }}</div>
            </div>
        </article>
        {% if not loop.last %}
            <div style="height: 5mm;"></div>
        {% endif %}
        {% endfor %}
    {% else %}
        <div style="text-align: center; padding: 50px 0; color: #666;">
            <h3>لا توجد أخبار تطابق معايير البحث</h3>
        </div>
    {% endif %}
    </div>

    <div class="page-number">صفحة </div>

    <div class="footer">
        <img src="{{ url_for('static', filename='img/logo2.png', _external=True) }}" alt="شعار نظام الرصد الإعلامي" class="footer-logo">
        <p style="margin: 5px 0;">© {{ current_year }} نظام الرصد الإعلامي</p>
        <p style="margin: 5px 0;">وحدة الرصد الإعلامي - قسم العلاقات والإعلام</p>
    </div>
</body>
</html>
