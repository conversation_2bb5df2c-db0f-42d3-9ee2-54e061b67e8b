<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أرشيف التقارير - نظام الرصد الإعلامي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .report-card { transition: transform 0.2s; }
        .report-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-newspaper me-2"></i>
                نظام الرصد الإعلامي
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                <a class="nav-link" href="{{ url_for('news_list') }}">الأخبار</a>
                <a class="nav-link" href="{{ url_for('print_news') }}">طباعة التقارير</a>
                <a class="nav-link active" href="{{ url_for('reports_archive') }}">أرشيف التقارير</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-archive me-2"></i>
                    أرشيف التقارير
                </h2>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">إجمالي التقارير</h5>
                        <h3 class="text-primary">{{ total_reports }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">التقارير اليومية</h5>
                        <h3 class="text-success">{{ daily_reports }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">تقارير الفترة</h5>
                        <h3 class="text-info">{{ period_reports }}</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة التقارير -->
        <div class="row">
            {% if reports.items %}
                {% for report in reports.items %}
                <div class="col-12 mb-3">
                    <div class="card report-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="card-title mb-2">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                        {{ report.filename or 'تقرير بدون اسم' }}
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <i class="fas fa-tag me-1"></i>
                                                النوع: {{ report.report_type or 'غير محدد' }}
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <i class="fas fa-newspaper me-1"></i>
                                                عدد الأخبار: {{ report.total_news or 0 }}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ التقرير: 
                                                {% if report.end_date %}
                                                    {% if report.end_date.strftime %}
                                                        {{ report.end_date.strftime('%Y-%m-%d') }}
                                                    {% else %}
                                                        {{ report.end_date }}
                                                    {% endif %}
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </small>
                                            <small class="text-muted ms-3">
                                                <i class="fas fa-clock me-1"></i>
                                                تم الإنشاء: 
                                                {% if report.created_at %}
                                                    {% if report.created_at.strftime %}
                                                        {{ report.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                    {% else %}
                                                        {{ report.created_at }}
                                                    {% endif %}
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_archived_report', report_id=report.id) }}" 
                                           class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fas fa-eye"></i>
                                            عرض
                                        </a>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteReport('{{ report.id }}', '{{ report.filename }}')">
                                            <i class="fas fa-trash"></i>
                                            حذف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تقارير في الأرشيف</h5>
                        <p class="text-muted">ابدأ بإنشاء تقارير من <a href="{{ url_for('print_news') }}">صفحة الطباعة</a></p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- تقسيم الصفحات -->
        {% if reports.has_next or reports.has_prev %}
        <nav aria-label="تقسيم الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if reports.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reports_archive', page=(reports.page - 1)) }}">السابق</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">{{ reports.page }}</span>
                </li>
                
                {% if reports.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reports_archive', page=(reports.page + 1)) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف التقرير "<span id="reportName"></span>" من الأرشيف؟</p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteReport(reportId, reportName) {
            document.getElementById('reportName').textContent = reportName;
            document.getElementById('deleteForm').action = `/reports/archive/${reportId}/delete`;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
