"""
نقل البيانات من قاعدة البيانات المحددة إلى Firebase
"""

import sqlite3
import os
from datetime import datetime, date

def migrate_specific_db_to_firebase():
    """نقل البيانات من قاعدة البيانات المحددة إلى Firebase"""
    
    db_path = r"G:\news\relational_relational_app.db"
    
    print("🚀 بدء نقل البيانات من قاعدة البيانات المحددة إلى Firebase...")
    print(f"📁 مسار قاعدة البيانات: {db_path}")
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
        return False
    
    try:
        # استيراد نماذج Firebase
        from firebase_models import Category, News, ReportArchive
        from firebase_config import test_connection
        
        # اختبار الاتصال بـ Firebase
        if not test_connection():
            print("❌ فشل الاتصال بـ Firebase")
            return False
        
        # الاتصال بقاعدة البيانات المحددة
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n📊 فحص الجداول المتاحة...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"الجداول الموجودة: {[table[0] for table in tables]}")
        
        print("\n📊 فحص البيانات في قاعدة البيانات...")
        
        # فحص التصنيفات
        try:
            cursor.execute("SELECT COUNT(*) FROM category")
            categories_count = cursor.fetchone()[0]
            print(f"   - التصنيفات: {categories_count}")
        except:
            categories_count = 0
            print("   - التصنيفات: جدول غير موجود")
        
        # فحص الأخبار
        try:
            cursor.execute("SELECT COUNT(*) FROM news")
            news_count = cursor.fetchone()[0]
            print(f"   - الأخبار: {news_count}")
        except:
            news_count = 0
            print("   - الأخبار: جدول غير موجود")
        
        # فحص التقارير
        try:
            cursor.execute("SELECT COUNT(*) FROM report_archive")
            reports_count = cursor.fetchone()[0]
            print(f"   - التقارير: {reports_count}")
        except:
            reports_count = 0
            print("   - التقارير: جدول غير موجود")
        
        if categories_count == 0 and news_count == 0 and reports_count == 0:
            print("⚠️ لا توجد بيانات للنقل")
            return True
        
        # 1. نقل التصنيفات
        category_mapping = {}
        categories_migrated = 0
        
        if categories_count > 0:
            print("\n📁 نقل التصنيفات...")
            try:
                cursor.execute("SELECT * FROM category")
                categories = cursor.fetchall()
                
                # فحص أعمدة جدول التصنيفات
                cursor.execute("PRAGMA table_info(category)")
                columns = [column[1] for column in cursor.fetchall()]
                print(f"   أعمدة جدول التصنيفات: {columns}")
                
                for cat in categories:
                    try:
                        # استخراج البيانات حسب الأعمدة المتاحة
                        cat_id = cat[0]  # أول عمود عادة هو ID
                        cat_name = cat[1] if len(cat) > 1 else f"تصنيف {cat_id}"
                        
                        # التحقق من وجود التصنيف مسبقاً
                        existing = Category.filter_by('name', cat_name, limit=1)
                        if existing:
                            category_mapping[cat_id] = existing[0].id
                            print(f"   ⚠️ موجود مسبقاً: {cat_name}")
                            continue
                        
                        new_category = Category.create({
                            'name': cat_name,
                            'description': f'تصنيف {cat_name}',
                            'created_at': datetime.now()
                        })
                        
                        if new_category:
                            category_mapping[cat_id] = new_category.id
                            categories_migrated += 1
                            print(f"   ✅ {cat_name}")
                        else:
                            print(f"   ❌ فشل: {cat_name}")
                            
                    except Exception as e:
                        print(f"   ❌ خطأ في نقل التصنيف: {str(e)}")
                
                print(f"   📊 تم نقل {categories_migrated} تصنيف")
                
            except Exception as e:
                print(f"❌ خطأ في نقل التصنيفات: {str(e)}")
        
        # 2. نقل الأخبار
        news_migrated = 0
        
        if news_count > 0:
            print("\n📰 نقل الأخبار...")
            try:
                cursor.execute("SELECT * FROM news")
                news_items = cursor.fetchall()
                
                # فحص أعمدة جدول الأخبار
                cursor.execute("PRAGMA table_info(news)")
                columns = [column[1] for column in cursor.fetchall()]
                print(f"   أعمدة جدول الأخبار: {columns}")
                
                for news_item in news_items:
                    try:
                        # استخراج البيانات (تكيف مع هيكل الجدول)
                        news_id = news_item[0]
                        title = news_item[1] if len(news_item) > 1 else f"خبر {news_id}"
                        content = news_item[2] if len(news_item) > 2 else ""
                        source = news_item[3] if len(news_item) > 3 else "مصدر غير محدد"
                        category_id = news_item[4] if len(news_item) > 4 else None
                        news_date = news_item[5] if len(news_item) > 5 else date.today()
                        
                        # تحويل التاريخ
                        if isinstance(news_date, str):
                            try:
                                news_date = datetime.strptime(news_date, '%Y-%m-%d').date()
                            except:
                                news_date = date.today()
                        elif news_date is None:
                            news_date = date.today()
                        
                        # ربط التصنيف الجديد
                        new_category_id = category_mapping.get(category_id, '') if category_id else ''
                        
                        new_news = News.create({
                            'title': title or f'خبر {news_id}',
                            'content': content or '',
                            'source': source or 'مصدر غير محدد',
                            'category_id': new_category_id,
                            'date': news_date,
                            'created_at': datetime.now()
                        })
                        
                        if new_news:
                            news_migrated += 1
                            if news_migrated % 10 == 0:
                                print(f"   📊 تم نقل {news_migrated} خبر...")
                        else:
                            print(f"   ❌ فشل في نقل: {title[:50]}...")
                            
                    except Exception as e:
                        print(f"   ❌ خطأ في نقل الخبر: {str(e)}")
                
                print(f"   📊 تم نقل {news_migrated} خبر")
                
            except Exception as e:
                print(f"❌ خطأ في نقل الأخبار: {str(e)}")
        
        # 3. نقل التقارير المؤرشفة
        reports_migrated = 0
        
        if reports_count > 0:
            print("\n📋 نقل التقارير...")
            try:
                cursor.execute("SELECT * FROM report_archive")
                reports = cursor.fetchall()
                
                # فحص أعمدة جدول التقارير
                cursor.execute("PRAGMA table_info(report_archive)")
                columns = [column[1] for column in cursor.fetchall()]
                print(f"   أعمدة جدول التقارير: {columns}")
                
                for report in reports:
                    try:
                        # استخراج البيانات
                        report_id = report[0]
                        filename = report[1] if len(report) > 1 else f"تقرير {report_id}"
                        report_type = report[2] if len(report) > 2 else "يومي"
                        start_date = report[3] if len(report) > 3 else date.today()
                        end_date = report[4] if len(report) > 4 else date.today()
                        category_id = report[5] if len(report) > 5 else None
                        total_news = report[6] if len(report) > 6 else 0
                        file_size = report[7] if len(report) > 7 else 0
                        html_content = report[8] if len(report) > 8 else ""
                        
                        # تحويل التواريخ
                        if isinstance(start_date, str):
                            try:
                                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                            except:
                                start_date = date.today()
                        
                        if isinstance(end_date, str):
                            try:
                                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                            except:
                                end_date = date.today()
                        
                        # ربط التصنيف الجديد
                        new_category_id = category_mapping.get(category_id, '') if category_id else ''
                        
                        new_report = ReportArchive.create({
                            'filename': filename or f'تقرير {report_id}',
                            'report_type': report_type or 'يومي',
                            'start_date': start_date,
                            'end_date': end_date,
                            'category_id': new_category_id,
                            'total_news': total_news or 0,
                            'file_size': file_size or 0,
                            'html_content': html_content or '',
                            'created_at': datetime.now()
                        })
                        
                        if new_report:
                            reports_migrated += 1
                            print(f"   ✅ {filename}")
                        else:
                            print(f"   ❌ فشل: {filename}")
                            
                    except Exception as e:
                        print(f"   ❌ خطأ في نقل التقرير: {str(e)}")
                
                print(f"   📊 تم نقل {reports_migrated} تقرير")
                
            except Exception as e:
                print(f"❌ خطأ في نقل التقارير: {str(e)}")
        
        # إغلاق الاتصال
        conn.close()
        
        # ملخص النقل
        print("\n🎉 ملخص النقل:")
        print(f"   ✅ التصنيفات: {categories_migrated}/{categories_count}")
        print(f"   ✅ الأخبار: {news_migrated}/{news_count}")
        print(f"   ✅ التقارير: {reports_migrated}/{reports_count}")
        
        total_migrated = categories_migrated + news_migrated + reports_migrated
        total_original = categories_count + news_count + reports_count
        
        print(f"\n📊 إجمالي البيانات المنقولة: {total_migrated}/{total_original}")
        
        if total_migrated > 0:
            print("🎉 تم نقل البيانات بنجاح إلى Firebase!")
            return True
        else:
            print("⚠️ لم يتم نقل أي بيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام في النقل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔄 نقل البيانات من قاعدة البيانات المحددة إلى Firebase")
    print("=" * 70)
    
    success = migrate_specific_db_to_firebase()
    
    if success:
        print("\n✅ تم النقل بنجاح! يمكنك الآن استخدام التطبيق مع Firebase.")
        print("🚀 تشغيل التطبيق: python app_firebase.py")
    else:
        print("\n❌ فشل النقل. راجع الأخطاء أعلاه.")
    
    print("=" * 70)
