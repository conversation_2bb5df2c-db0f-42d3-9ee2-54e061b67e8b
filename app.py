from flask import Flask, render_template, redirect, url_for, request, jsonify, flash, make_response, send_file, session, abort
from flask_migrate import Migrate
from datetime import datetime, date, timedelta
import json
import os
import sys
import pdfkit
import locale
import shutil
import sqlite3
from sqlalchemy import and_, or_
from datetime import datetime, date
from werkzeug.utils import secure_filename

# تعطيل استخدام pdfkit
pdf_enabled = False

# دالة لتحويل التاريخ إلى اللغة العربية
def format_date_arabic(date_obj, format_type='full'):
    """
    تحويل التاريخ إلى اللغة العربية بعدة صيغ

    :param date_obj: كائن التاريخ
    :param format_type: نوع الصيغة ('full', 'medium', 'short', 'day_month', 'month_year', 'numeric')
    :return: التاريخ بالصيغة العربية
    """
    if not date_obj:
        return ""

    # قائمة بأسماء الأيام باللغة العربية
    arabic_days = [
        "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"
    ]

    # قائمة بأسماء الأيام المختصرة باللغة العربية
    arabic_days_short = [
        "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت", "أحد"
    ]

    # قائمة بأسماء الأشهر باللغة العربية (مصر والخليج)
    arabic_months_egypt = [
        "يناير", "فبراير", "مارس", "إبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ]

    # قائمة بأسماء الأشهر باللغة العربية (العراق وسوريا ولبنان وفلسطين والأردن)
    arabic_months_levant = [
        "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
        "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
    ]

    # الحصول على اليوم والشهر والسنة
    day_name = arabic_days[date_obj.weekday()]
    day_name_short = arabic_days_short[date_obj.weekday()]
    day = date_obj.day
    month_egypt = arabic_months_egypt[date_obj.month - 1]
    month_levant = arabic_months_levant[date_obj.month - 1]
    year = date_obj.year

    # تنسيق التاريخ بالعربية حسب النوع المطلوب
    if format_type == 'full':
        # مثال: الخميس 17 مايو 2024
        formatted_date = f"{day_name} {day} {month_egypt} {year}"
    elif format_type == 'full_levant':
        # مثال: الخميس 17 أيار 2024
        formatted_date = f"{day_name} {day} {month_levant} {year}"
    elif format_type == 'medium':
        # مثال: 17 مايو 2024
        formatted_date = f"{day} {month_egypt} {year}"
    elif format_type == 'medium_levant':
        # مثال: 17 أيار 2024
        formatted_date = f"{day} {month_levant} {year}"
    elif format_type == 'short':
        # مثال: 17 مايو
        formatted_date = f"{day} {month_egypt}"
    elif format_type == 'short_levant':
        # مثال: 17 أيار
        formatted_date = f"{day} {month_levant}"
    elif format_type == 'day_month':
        # مثال: الخميس 17 مايو
        formatted_date = f"{day_name} {day} {month_egypt}"
    elif format_type == 'day_month_levant':
        # مثال: الخميس 17 أيار
        formatted_date = f"{day_name} {day} {month_levant}"
    elif format_type == 'month_year':
        # مثال: مايو 2024
        formatted_date = f"{month_egypt} {year}"
    elif format_type == 'month_year_levant':
        # مثال: أيار 2024
        formatted_date = f"{month_levant} {year}"
    elif format_type == 'numeric':
        # مثال: 17/05/2024
        formatted_date = f"{day:02d}/{date_obj.month:02d}/{year}"
    elif format_type == 'numeric_dash':
        # مثال: 17-05-2024
        formatted_date = f"{day:02d}-{date_obj.month:02d}-{year}"
    else:
        # الصيغة الافتراضية
        formatted_date = f"{day_name} {day} {month_egypt} {year}"

    return formatted_date

# إضافة المجلد الحالي إلى مسار البحث
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from config import Config

# إنشاء تطبيق Flask
app = Flask(__name__,
            template_folder='app/templates',
            static_folder='app/static')
app.config.from_object(Config)

# إضافة دالة مساعدة لتنسيق التاريخ في قوالب Jinja2
@app.template_filter('format_date_arabic')
def format_date_arabic_filter(date_obj, format_type='full'):
    """
    فلتر Jinja2 لتنسيق التاريخ بالعربية

    :param date_obj: كائن التاريخ
    :param format_type: نوع الصيغة ('full', 'medium', 'short', 'day_month', 'month_year', 'numeric')
    :return: التاريخ بالصيغة العربية
    """
    return format_date_arabic(date_obj, format_type)

# تغيير اسم قاعدة البيانات
app.config['SQLALCHEMY_DATABASE_URI'] = app.config['SQLALCHEMY_DATABASE_URI'].replace('app.db', 'relational_app.db')

# استخدام النماذج العلائقية الجديدة
from app.models.relational_models import db, News, Category, Governorate, Field, FieldValue, NewsSource, SourceType, ReportArchive, init_governorates
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('app')

# تهيئة قاعدة البيانات
db.init_app(app)
migrate = Migrate(app, db)

# تم إزالة تهيئة المجدول

# إضافة فلتر nl2br لتحويل السطور الجديدة إلى وسوم <br>
@app.template_filter('nl2br')
def nl2br(value):
    if value:
        # تحويل السطور الجديدة إلى وسوم <br>
        value = value.replace('\n', '<br>\n')
        # معالجة وسوم <br> المكتوبة كنص
        value = value.replace('&lt;br&gt;', '<br>')
        value = value.replace('<br>', '<br>')
    return value

# دالة لتنسيق التاريخ باللغة العربية
def format_date_arabic(date_obj, format_type='default'):
    if not date_obj:
        return ""

    arabic_months = {
        1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل", 5: "مايو", 6: "يونيو",
        7: "يوليو", 8: "أغسطس", 9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
    }

    arabic_months_levant = {
        1: "كانون الثاني", 2: "شباط", 3: "آذار", 4: "نيسان", 5: "أيار", 6: "حزيران",
        7: "تموز", 8: "آب", 9: "أيلول", 10: "تشرين الأول", 11: "تشرين الثاني", 12: "كانون الأول"
    }

    # قائمة بأسماء الأيام باللغة العربية
    arabic_days = {
        0: "الاثنين", 1: "الثلاثاء", 2: "الأربعاء", 3: "الخميس", 4: "الجمعة", 5: "السبت", 6: "الأحد"
    }

    if format_type == 'full_with_day':
        # مثال: يوم الخميس 17 مايو 2024
        day_name = arabic_days[date_obj.weekday()]
        return f"يوم {day_name} {date_obj.day} {arabic_months[date_obj.month]} {date_obj.year}"
    elif format_type == 'full_with_day_levant':
        # مثال: يوم الخميس 17 أيار 2024
        day_name = arabic_days[date_obj.weekday()]
        return f"يوم {day_name} {date_obj.day} {arabic_months_levant[date_obj.month]} {date_obj.year}"
    elif format_type == 'full_levant':
        return f"{date_obj.day} {arabic_months_levant[date_obj.month]} {date_obj.year}"
    elif format_type == 'full':
        return f"{date_obj.day} {arabic_months[date_obj.month]} {date_obj.year}"
    else:
        return f"{date_obj.day}/{date_obj.month}/{date_obj.year}"

# تمكين استخدام وسوم HTML آمنة في القوالب
app.jinja_env.filters['nl2br'] = nl2br
app.jinja_env.filters['format_date_arabic'] = format_date_arabic
app.jinja_env.autoescape = False

# دالة تنفذ قبل كل طلب لتمرير أحدث الأخبار إلى جميع القوالب
@app.context_processor
def inject_latest_news():
    latest_news = News.query.order_by(News.date.desc()).limit(5).all()
    return dict(latest_news=latest_news)

with app.app_context():
    db.create_all()
    init_governorates(db.session)

# الصفحة الرئيسية
@app.route('/')
def index():
    # جلب آخر 10 أخبار
    latest_news = News.query.order_by(News.date.desc()).limit(10).all()

    # جلب أخبار مميزة (أحدث 6 أخبار من تصنيفات مختلفة)
    featured_news = News.query.order_by(News.date.desc()).limit(6).all()

    # جلب التصنيفات
    categories = Category.query.all()

    # جلب المحافظات
    governorates = Governorate.query.all()

    # الوقت الحالي لحساب الوقت المنقضي
    now = datetime.now()

    # إحصائيات النظام
    news_count = News.query.count()
    categories_count = Category.query.count()
    governorates_count = Governorate.query.count()
    today = format_date_arabic(date.today())

    return render_template('index.html',
                          latest_news=latest_news,
                          featured_news=featured_news,
                          categories=categories,
                          governorates=governorates,
                          news_count=news_count,
                          categories_count=categories_count,
                          governorates_count=governorates_count,
                          today=today,
                          now=now)

# صفحة إضافة خبر جديد
@app.route('/news/add', methods=['GET', 'POST'])
def add_news():
    if request.method == 'POST':
        # استلام بيانات النموذج
        title = request.form.get('title')
        content = request.form.get('content')
        # معالجة وسوم <br> في المحتوى
        content = content.replace('<br>', '\n')
        date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        source = request.form.get('source')
        source_url = request.form.get('source_url')
        governorate_id = request.form.get('governorate_id', type=int)
        category_id = request.form.get('category_id', type=int)

        # إنشاء خبر جديد
        news = News(
            title=title,
            content=content,
            date=date,
            source=source,
            source_url=source_url,
            governorate_id=governorate_id,
            category_id=category_id
        )
        db.session.add(news)
        db.session.flush()  # للحصول على معرف الخبر الجديد

        # معالجة الحقول الديناميكية
        category = Category.query.get(category_id)
        fields = Field.query.filter_by(category_id=category_id).all()

        for field in fields:
            field_value = request.form.get(f'field_{field.id}')
            if field_value:
                field_value_obj = FieldValue(
                    news_id=news.id,
                    field_id=field.id,
                    value=field_value
                )
                db.session.add(field_value_obj)

        db.session.commit()
        flash('تمت إضافة الخبر بنجاح', 'success')
        return redirect(url_for('view_news'))

    # عرض نموذج إضافة خبر
    governorates = Governorate.query.all()
    categories = Category.query.all()

    return render_template('add_news.html',
                           governorates=governorates,
                           categories=categories)

# الحصول على الحقول الديناميكية للتصنيف المحدد
@app.route('/api/category/<int:category_id>/fields')
def get_category_fields(category_id):
    fields = Field.query.filter_by(category_id=category_id).order_by(Field.order).all()
    fields_data = []

    for field in fields:
        field_data = {
            'id': field.id,
            'name': field.name,
            'type': field.field_type,
            'required': field.required,
            'options': field.get_options() if field.field_type == 'select' else []
        }
        fields_data.append(field_data)

    return jsonify(fields_data)

# صفحة عرض الأخبار
@app.route('/news')
def view_news():
    # تحديد رقم الصفحة وعدد العناصر في الصفحة
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد الأخبار في الصفحة الواحدة

    # استلام معايير الفلترة
    category_id = request.args.get('category_id', '')
    governorate_id = request.args.get('governorate_id', '')
    date_filter = request.args.get('date', '')
    search_query = request.args.get('search', '')

    # إنشاء استعلام الأخبار
    query = News.query

    # تطبيق معايير الفلترة
    if category_id and category_id.isdigit():
        query = query.filter(News.category_id == int(category_id))

    if governorate_id and governorate_id.isdigit():
        query = query.filter(News.governorate_id == int(governorate_id))

    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(News.date == filter_date)
        except ValueError:
            # في حالة وجود خطأ في تنسيق التاريخ، تجاهل هذا الفلتر
            pass

    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                News.title.like(search_term),
                News.content.like(search_term),
                News.source.like(search_term)
            )
        )

    # ترتيب الأخبار حسب التاريخ تنازلياً
    query = query.order_by(News.date.desc())

    # تطبيق تقسيم الصفحات
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)

    news_list = pagination.items
    categories = Category.query.all()
    governorates = Governorate.query.all()

    return render_template('view_news.html',
                          news_list=news_list,
                          categories=categories,
                          governorates=governorates,
                          pagination=pagination,
                          category_id=category_id,
                          governorate_id=governorate_id,
                          date_filter=date_filter,
                          search_query=search_query)

# صفحة عرض الأخبار حسب التصنيف
@app.route('/news/category/<int:category_id>')
def view_news_by_category(category_id):
    category = Category.query.get_or_404(category_id)
    news_list = News.query.filter_by(category_id=category_id).order_by(News.date.desc()).all()
    categories = Category.query.all()
    governorates = Governorate.query.all()

    # الوقت الحالي لحساب الوقت المنقضي
    now = datetime.now()

    return render_template('news_by_category.html',
                          news_list=news_list,
                          category=category,
                          categories=categories,
                          governorates=governorates,
                          now=now)

# صفحة عرض الأخبار حسب المحافظة
@app.route('/news/governorate/<int:governorate_id>')
def view_news_by_governorate(governorate_id):
    governorate = Governorate.query.get_or_404(governorate_id)
    news_list = News.query.filter_by(governorate_id=governorate_id).order_by(News.date.desc()).all()
    categories = Category.query.all()
    governorates = Governorate.query.all()

    # الوقت الحالي لحساب الوقت المنقضي
    now = datetime.now()

    return render_template('news_by_governorate.html',
                          news_list=news_list,
                          governorate=governorate,
                          categories=categories,
                          governorates=governorates,
                          now=now)

# صفحة عرض تفاصيل الخبر
@app.route('/news/<int:news_id>')
def view_news_details(news_id):
    news = News.query.get_or_404(news_id)
    field_values = FieldValue.query.filter_by(news_id=news_id).all()

    # تنظيم قيم الحقول
    field_data = []
    for fv in field_values:
        field = Field.query.get(fv.field_id)
        field_data.append({
            'name': field.name,
            'value': fv.value,
            'type': field.field_type
        })

    return render_template('news_details.html', news=news, field_data=field_data)

# صفحة تعديل الخبر
@app.route('/news/edit/<int:news_id>', methods=['GET', 'POST'])
def edit_news(news_id):
    news = News.query.get_or_404(news_id)

    if request.method == 'POST':
        # استلام بيانات النموذج
        news.title = request.form.get('title')
        content = request.form.get('content')
        # معالجة وسوم <br> في المحتوى
        content = content.replace('<br>', '\n')
        news.content = content
        news.date = datetime.strptime(request.form.get('date'), '%Y-%m-%d').date()
        news.source = request.form.get('source')
        news.source_url = request.form.get('source_url')
        news.governorate_id = request.form.get('governorate_id', type=int)
        news.category_id = request.form.get('category_id', type=int)

        # حذف قيم الحقول الديناميكية القديمة
        FieldValue.query.filter_by(news_id=news_id).delete()

        # معالجة الحقول الديناميكية الجديدة
        fields = Field.query.filter_by(category_id=news.category_id).all()

        for field in fields:
            field_value = request.form.get(f'field_{field.id}')
            if field_value:
                field_value_obj = FieldValue(
                    news_id=news.id,
                    field_id=field.id,
                    value=field_value
                )
                db.session.add(field_value_obj)

        db.session.commit()
        flash('تم تعديل الخبر بنجاح', 'success')
        return redirect(url_for('view_news_details', news_id=news.id))

    # عرض نموذج تعديل الخبر
    governorates = Governorate.query.all()
    categories = Category.query.all()
    field_values = FieldValue.query.filter_by(news_id=news_id).all()

    return render_template('edit_news.html',
                           news=news,
                           governorates=governorates,
                           categories=categories,
                           field_values=field_values)

# حذف الخبر
@app.route('/news/delete/<int:news_id>', methods=['POST'])
def delete_news(news_id):
    news = News.query.get_or_404(news_id)

    # حذف قيم الحقول الديناميكية المرتبطة بالخبر
    FieldValue.query.filter_by(news_id=news_id).delete()

    # حذف الخبر
    db.session.delete(news)
    db.session.commit()

    flash('تم حذف الخبر بنجاح', 'success')
    return redirect(url_for('view_news'))

# صفحة طباعة الأخبار
@app.route('/news/print')
def print_news():
    # استلام معايير البحث
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    category_id = request.args.get('category_id', '')

    # إنشاء استعلام الأخبار
    query = News.query

    # تطبيق معايير البحث
    if start_date:
        query = query.filter(News.date >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date:
        query = query.filter(News.date <= datetime.strptime(end_date, '%Y-%m-%d').date())
    if category_id and category_id.isdigit():
        query = query.filter(News.category_id == int(category_id))

    # ترتيب الأخبار حسب التاريخ تنازلياً
    news_list = query.order_by(News.date.desc()).all()

    # جلب التصنيفات للفلتر
    categories = Category.query.all()

    # تحضير بيانات القالب
    today_date = format_date_arabic(date.today())
    current_year = date.today().year

    return render_template('print_news.html',
                          news_list=news_list,
                          categories=categories,
                          today_date=today_date,
                          current_year=current_year)

# تصدير الأخبار إلى PDF
@app.route('/news/export/pdf')
def export_news_pdf():
    try:
        # استلام معايير البحث
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        category_id = request.args.get('category_id', '')

        # إنشاء استعلام الأخبار
        query = News.query.options(
            db.joinedload(News.category),
            db.joinedload(News.governorate)
        )

        # تطبيق معايير البحث
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(News.date >= start_date_obj)
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(News.date <= end_date_obj)
        if category_id and category_id.isdigit():
            query = query.filter(News.category_id == int(category_id))

        # ترتيب الأخبار حسب التاريخ تنازلياً مع تحديد عدد النتائج
        news_list = query.order_by(News.date.desc()).limit(1000).all()

        # تحديد التاريخ المستخدم في التقرير
        if end_date:
            # استخدام التاريخ من خانة "إلى"
            report_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            report_date_formatted = format_date_arabic(report_date, 'full_with_day')
        else:
            # استخدام التاريخ الحالي إذا لم يتم تحديد تاريخ نهاية
            report_date = date.today()
            report_date_formatted = format_date_arabic(report_date, 'full_with_day')

        # تحضير بيانات القالب
        current_year = report_date.year

        # إنشاء اسم الملف حسب نوع التقرير
        if start_date and end_date and start_date != end_date:
            # تقرير لفترة (عدة أيام)
            start_date_formatted = format_date_arabic(datetime.strptime(start_date, '%Y-%m-%d').date())
            end_date_formatted = format_date_arabic(datetime.strptime(end_date, '%Y-%m-%d').date())
            filename = f"تقرير الرصد الاعلامي لفترة من {start_date_formatted} الى {end_date_formatted}"
        else:
            # تقرير ليوم واحد
            filename = f"الرصد الاعلامي ليوم {report_date_formatted}"

        # تحضير معلومات إضافية للتقرير
        total_news = len(news_list)
        date_range_text = ""
        if start_date and end_date:
            # التحقق من أن التاريخين مختلفان
            if start_date != end_date:
                start_date_formatted = format_date_arabic(datetime.strptime(start_date, '%Y-%m-%d').date())
                end_date_formatted = format_date_arabic(datetime.strptime(end_date, '%Y-%m-%d').date())
                date_range_text = f"من {start_date_formatted} إلى {end_date_formatted}"
            # إذا كان التاريخان متطابقان، لا نعرض فترة التقرير
        elif start_date:
            start_date_formatted = format_date_arabic(datetime.strptime(start_date, '%Y-%m-%d').date())
            date_range_text = f"من {start_date_formatted}"
        elif end_date:
            end_date_formatted = format_date_arabic(datetime.strptime(end_date, '%Y-%m-%d').date())
            date_range_text = f"حتى {end_date_formatted}"

        # إنشاء محتوى HTML للتقرير
        html_content = render_template('pdf_template.html',
                                     news_list=news_list,
                                     today_date=report_date_formatted,
                                     current_year=current_year,
                                     filename=filename,
                                     total_news=total_news,
                                     date_range_text=date_range_text,
                                     start_date=start_date,
                                     end_date=end_date)

        # حفظ التقرير في الأرشيف
        try:
            # تحديد نوع التقرير
            if start_date and end_date and start_date != end_date:
                report_type = "فترة"
            else:
                report_type = "يومي"

            # تحويل التواريخ النصية إلى كائنات تاريخ
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
            category_id_int = int(category_id) if category_id and category_id.isdigit() else None

            # حساب حجم المحتوى
            file_size = len(html_content.encode('utf-8'))

            # إنشاء سجل أرشيف جديد
            archive_record = ReportArchive(
                filename=filename,
                report_type=report_type,
                start_date=start_date_obj,
                end_date=end_date_obj,
                category_id=category_id_int,
                total_news=total_news,
                file_size=file_size,
                html_content=html_content
            )

            db.session.add(archive_record)
            db.session.commit()

            print(f"تم حفظ التقرير في الأرشيف: {filename}")

        except Exception as archive_error:
            print(f"خطأ في حفظ التقرير في الأرشيف: {str(archive_error)}")
            # لا نوقف العملية إذا فشل الحفظ في الأرشيف

        # عرض التقرير
        response = make_response(html_content)

        # إضافة header لاسم الملف عند التصدير (بترميز UTF-8)
        import urllib.parse
        encoded_filename = urllib.parse.quote(f"{filename}.pdf")
        response.headers['Content-Disposition'] = f"inline; filename*=UTF-8''{encoded_filename}"

        return response

    except Exception as e:
        print(f"خطأ في تصدير PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return f"حدث خطأ في تصدير PDF: {str(e)}", 500

# صفحة أرشيف التقارير
@app.route('/reports/archive')
def reports_archive():
    # جلب جميع التقارير المؤرشفة مرتبة حسب تاريخ الإنشاء
    page = request.args.get('page', 1, type=int)
    per_page = 20  # عدد التقارير في كل صفحة

    # فلترة حسب نوع التقرير
    report_type = request.args.get('report_type', '')

    # فلترة حسب التصنيف
    category_id = request.args.get('category_id', '')

    # فلترة حسب التاريخ
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # بناء الاستعلام
    query = ReportArchive.query

    if report_type:
        query = query.filter(ReportArchive.report_type == report_type)

    if category_id and category_id.isdigit():
        query = query.filter(ReportArchive.category_id == int(category_id))

    if date_from:
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        query = query.filter(ReportArchive.created_at >= date_from_obj)

    if date_to:
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
        query = query.filter(ReportArchive.created_at <= date_to_obj)

    # ترتيب وتقسيم الصفحات
    reports = query.order_by(ReportArchive.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    # جلب التصنيفات للفلترة
    categories = Category.query.all()

    # إحصائيات الأرشيف
    total_reports = ReportArchive.query.count()
    daily_reports = ReportArchive.query.filter(ReportArchive.report_type == 'يومي').count()
    period_reports = ReportArchive.query.filter(ReportArchive.report_type == 'فترة').count()

    return render_template('reports_archive.html',
                         reports=reports,
                         categories=categories,
                         total_reports=total_reports,
                         daily_reports=daily_reports,
                         period_reports=period_reports,
                         report_type=report_type,
                         category_id=category_id,
                         date_from=date_from,
                         date_to=date_to)

# عرض تقرير مؤرشف
@app.route('/reports/archive/<int:report_id>')
def view_archived_report(report_id):
    report = ReportArchive.query.get_or_404(report_id)

    # إرجاع محتوى HTML المحفوظ
    return report.html_content

# حذف تقرير من الأرشيف
@app.route('/reports/archive/<int:report_id>/delete', methods=['POST'])
def delete_archived_report(report_id):
    report = ReportArchive.query.get_or_404(report_id)

    try:
        db.session.delete(report)
        db.session.commit()
        flash(f'تم حذف التقرير "{report.filename}" من الأرشيف بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ في حذف التقرير: {str(e)}', 'danger')

    return redirect(url_for('reports_archive'))

# صفحة إدارة التصنيفات
@app.route('/categories', methods=['GET'])
def manage_categories():
    categories = Category.query.all()

    # إضافة عدد الحقول لكل تصنيف
    categories_data = []
    for category in categories:
        fields_count = Field.query.filter_by(category_id=category.id).count()
        categories_data.append({
            'id': category.id,
            'name': category.name,
            'description': category.description,
            'fields_count': fields_count
        })

    return render_template('manage_categories.html', categories=categories_data)

# صفحة إضافة تصنيف جديد
@app.route('/categories/add', methods=['GET', 'POST'])
def add_category():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        # إنشاء تصنيف جديد
        category = Category(name=name, description=description)
        db.session.add(category)
        db.session.flush()  # للحصول على معرف التصنيف الجديد

        # معالجة الحقول الديناميكية
        field_names = request.form.getlist('field_name')
        field_types = request.form.getlist('field_type')
        field_required = request.form.getlist('field_required')
        field_options = request.form.getlist('field_options')

        for i in range(len(field_names)):
            if field_names[i]:
                field = Field(
                    name=field_names[i],
                    field_type=field_types[i],
                    required=i < len(field_required) and field_required[i] == 'on',
                    category_id=category.id,
                    order=i
                )

                # إضافة الخيارات إذا كان نوع الحقل قائمة منسدلة
                if field.field_type == 'select' and i < len(field_options) and field_options[i]:
                    options = [opt.strip() for opt in field_options[i].split(',') if opt.strip()]
                    field.set_options(options)

                db.session.add(field)

        db.session.commit()
        flash('تمت إضافة التصنيف بنجاح', 'success')
        return redirect(url_for('manage_categories'))

    return render_template('add_category.html')

# صفحة تعديل تصنيف
@app.route('/categories/edit/<int:category_id>', methods=['GET', 'POST'])
def edit_category(category_id):
    category = Category.query.get_or_404(category_id)
    fields = Field.query.filter_by(category_id=category_id).order_by(Field.order).all()

    if request.method == 'POST':
        category.name = request.form.get('name')
        category.description = request.form.get('description')

        # حذف الحقول القديمة
        for field in fields:
            db.session.delete(field)

        # إضافة الحقول الجديدة
        field_names = request.form.getlist('field_name')
        field_types = request.form.getlist('field_type')
        field_required = request.form.getlist('field_required')
        field_options = request.form.getlist('field_options')

        for i in range(len(field_names)):
            if field_names[i]:
                field = Field(
                    name=field_names[i],
                    field_type=field_types[i],
                    required=i < len(field_required) and field_required[i] == 'on',
                    category_id=category.id,
                    order=i
                )

                # إضافة الخيارات إذا كان نوع الحقل قائمة منسدلة
                if field.field_type == 'select' and i < len(field_options) and field_options[i]:
                    options = [opt.strip() for opt in field_options[i].split(',') if opt.strip()]
                    field.set_options(options)

                db.session.add(field)

        db.session.commit()
        flash('تم تعديل التصنيف بنجاح', 'success')
        return redirect(url_for('manage_categories'))

    return render_template('edit_category.html', category=category, fields=fields)

# حذف تصنيف
@app.route('/categories/delete/<int:category_id>', methods=['POST'])
def delete_category(category_id):
    category = Category.query.get_or_404(category_id)

    # التحقق من عدم وجود أخبار مرتبطة بهذا التصنيف
    if News.query.filter_by(category_id=category_id).count() > 0:
        flash('لا يمكن حذف التصنيف لأنه يحتوي على أخبار', 'danger')
        return redirect(url_for('manage_categories'))

    # حذف الحقول المرتبطة بالتصنيف
    fields = Field.query.filter_by(category_id=category_id).all()
    for field in fields:
        db.session.delete(field)

    # حذف التصنيف
    db.session.delete(category)
    db.session.commit()
    flash('تم حذف التصنيف بنجاح', 'success')
    return redirect(url_for('manage_categories'))

# صفحة إدارة المحافظات
@app.route('/governorates')
def manage_governorates():
    governorates = Governorate.query.all()

    # إضافة عدد الأخبار لكل محافظة
    governorates_data = []
    for governorate in governorates:
        news_count = News.query.filter_by(governorate_id=governorate.id).count()
        governorates_data.append({
            'id': governorate.id,
            'name': governorate.name,
            'news_count': news_count
        })

    return render_template('manage_governorates.html', governorates=governorates_data)

# إضافة محافظة جديدة
@app.route('/governorates/add', methods=['POST'])
def add_governorate():
    name = request.form.get('name')

    # التحقق من عدم وجود محافظة بنفس الاسم
    if Governorate.query.filter_by(name=name).first():
        flash('المحافظة موجودة بالفعل', 'danger')
        return redirect(url_for('manage_governorates'))

    # إنشاء محافظة جديدة
    governorate = Governorate(name=name)
    db.session.add(governorate)
    db.session.commit()

    flash('تمت إضافة المحافظة بنجاح', 'success')
    return redirect(url_for('manage_governorates'))

# تعديل محافظة
@app.route('/governorates/edit/<int:governorate_id>', methods=['POST'])
def edit_governorate(governorate_id):
    governorate = Governorate.query.get_or_404(governorate_id)
    name = request.form.get('name')

    # التحقق من عدم وجود محافظة أخرى بنفس الاسم
    existing = Governorate.query.filter_by(name=name).first()
    if existing and existing.id != governorate_id:
        flash('يوجد محافظة أخرى بنفس الاسم', 'danger')
        return redirect(url_for('manage_governorates'))

    # تعديل المحافظة
    governorate.name = name
    db.session.commit()

    flash('تم تعديل المحافظة بنجاح', 'success')
    return redirect(url_for('manage_governorates'))

# حذف محافظة
@app.route('/governorates/delete/<int:governorate_id>', methods=['POST'])
def delete_governorate(governorate_id):
    governorate = Governorate.query.get_or_404(governorate_id)

    # التحقق من عدم وجود أخبار مرتبطة بهذه المحافظة
    if News.query.filter_by(governorate_id=governorate_id).count() > 0:
        flash('لا يمكن حذف المحافظة لأنها تحتوي على أخبار', 'danger')
        return redirect(url_for('manage_governorates'))

    # حذف المحافظة
    db.session.delete(governorate)
    db.session.commit()

    flash('تم حذف المحافظة بنجاح', 'success')
    return redirect(url_for('manage_governorates'))

# صفحة إدارة مصادر الأخبار
@app.route('/sources')
def manage_sources():
    sources = NewsSource.query.all()
    return render_template('manage_sources.html', sources=sources)

# إضافة مصدر جديد
@app.route('/sources/add', methods=['POST'])
def add_source():
    name = request.form.get('name')
    url = request.form.get('url')
    source_type = request.form.get('source_type', SourceType.WEBSITE.value)
    is_active = 'is_active' in request.form

    # إنشاء مصدر جديد
    source = NewsSource(
        name=name,
        url=url,
        source_type=source_type,
        is_active=is_active
    )

    # تحديد ما إذا كان المصدر عراقي
    from urllib.parse import urlparse
    domain = urlparse(url).netloc
    source.is_iraqi = '.iq' in domain or any(keyword in domain for keyword in ['iraq', 'عراق'])

    db.session.add(source)
    db.session.commit()

    flash('تمت إضافة المصدر بنجاح', 'success')
    return redirect(url_for('manage_sources'))

# تعديل مصدر
@app.route('/sources/edit/<int:source_id>', methods=['POST'])
def edit_source(source_id):
    source = NewsSource.query.get_or_404(source_id)

    source.name = request.form.get('name')
    source.url = request.form.get('url')
    source.source_type = request.form.get('source_type', SourceType.WEBSITE.value)
    source.is_active = 'is_active' in request.form

    # تحديد ما إذا كان المصدر عراقي
    from urllib.parse import urlparse
    domain = urlparse(source.url).netloc
    source.is_iraqi = '.iq' in domain or any(keyword in domain for keyword in ['iraq', 'عراق'])

    db.session.commit()

    flash('تم تعديل المصدر بنجاح', 'success')
    return redirect(url_for('manage_sources'))

# حذف مصدر
@app.route('/sources/delete/<int:source_id>', methods=['POST'])
def delete_source(source_id):
    source = NewsSource.query.get_or_404(source_id)

    # حذف المصدر
    db.session.delete(source)
    db.session.commit()

    flash('تم حذف المصدر بنجاح', 'success')
    return redirect(url_for('manage_sources'))

# تم إزالة دالة التحقق من جاهزية المصادر

# تم إزالة دالة الحصول على نتائج التحقق من المصادر

# تم إزالة دالة تحديث حالة المصادر

# تم إزالة دوال إدارة المصادر غير الفعالة

# تم إزالة وظيفة جلب الأخبار من جميع المصادر

# تم إزالة وظيفة تشغيل المجدول يدويًا

# تم إزالة وظيفة جلب الأخبار من مصدر محدد

# تم إزالة وظيفة حفظ الأخبار المستخرجة

# تم إزالة وظيفة عرض سجلات الجلب

# تم إزالة صفحة الجلب الآلي للأخبار

# تم إزالة إعادة توجيه الصفحة القديمة

# تم إزالة دالة تنظيف الأخبار التلقائية

# تم إزالة دالة جلب الأخبار من المصادر

# تم إزالة دالة حفظ الأخبار المجلوبة تلقائيًا


# صفحة الإعدادات
@app.route('/settings')
def settings():
    # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    # الحصول على قائمة النسخ الاحتياطية
    backups = []

    # التحقق من وجود المجلد وإمكانية الوصول إليه
    if os.path.exists(backup_dir) and os.access(backup_dir, os.R_OK):
        for filename in os.listdir(backup_dir):
            if filename.endswith(('.sqlite', '.db', '.backup')):
                file_path = os.path.join(backup_dir, filename)
                file_size = os.path.getsize(file_path)
                file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

                # تحويل حجم الملف إلى صيغة مقروءة
                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                backups.append({
                    'filename': filename,
                    'size': size_str,
                    'date': file_date,
                    'path': file_path
                })

    # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
    backups.sort(key=lambda x: x['date'], reverse=True)

    return render_template('settings.html', backups=backups)

# إنشاء نسخة احتياطية
@app.route('/backup/create', methods=['POST'])
def create_backup():
    try:
        # الحصول على مسار قاعدة البيانات الحالية
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']

        # طباعة مسار قاعدة البيانات للتصحيح
        print(f"Database URI: {db_uri}")

        # التعامل مع مسارات قاعدة البيانات المختلفة
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            # إذا كان المسار نسبيًا، قم بتحويله إلى مسار مطلق
            if not os.path.isabs(db_path):
                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        else:
            flash('نوع قاعدة البيانات غير مدعوم للنسخ الاحتياطي', 'danger')
            return redirect(url_for('settings'))

        # التحقق من وجود ملف قاعدة البيانات
        if not os.path.exists(db_path):
            flash(f'ملف قاعدة البيانات غير موجود في المسار: {db_path}', 'danger')
            return redirect(url_for('settings'))

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # إنشاء اسم ملف النسخة الاحتياطية بالتاريخ والوقت
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{timestamp}.sqlite"
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ ملف قاعدة البيانات
        shutil.copy2(db_path, backup_path)

        flash('تم إنشاء نسخة احتياطية بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'danger')
        # طباعة تفاصيل الخطأ للتصحيح
        import traceback
        print(traceback.format_exc())

    return redirect(url_for('settings'))

# تنزيل نسخة احتياطية
@app.route('/backup/download/<filename>')
def download_backup(filename):
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
    backup_path = os.path.join(backup_dir, filename)

    if not os.path.exists(backup_path):
        flash('النسخة الاحتياطية غير موجودة', 'warning')
        return redirect(url_for('settings'))

    return send_file(backup_path, as_attachment=True)

# حذف نسخة احتياطية
@app.route('/backup/delete/<filename>')
def delete_backup(filename):
    try:
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
        backup_path = os.path.join(backup_dir, filename)

        if os.path.exists(backup_path):
            os.remove(backup_path)
            flash('تم حذف النسخة الاحتياطية بنجاح', 'success')
        else:
            flash('النسخة الاحتياطية غير موجودة', 'warning')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}', 'danger')
        # طباعة تفاصيل الخطأ للتصحيح
        import traceback
        print(traceback.format_exc())

    return redirect(url_for('settings'))

# استعادة نسخة احتياطية
@app.route('/backup/restore/<filename>')
def restore_backup(filename):
    try:
        # الحصول على مسار قاعدة البيانات الحالية
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']

        # طباعة مسار قاعدة البيانات للتصحيح
        print(f"Database URI: {db_uri}")

        # التعامل مع مسارات قاعدة البيانات المختلفة
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            # إذا كان المسار نسبيًا، قم بتحويله إلى مسار مطلق
            if not os.path.isabs(db_path):
                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        else:
            flash('نوع قاعدة البيانات غير مدعوم للاستعادة', 'danger')
            return redirect(url_for('settings'))

        # مسار النسخة الاحتياطية
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
        backup_path = os.path.join(backup_dir, filename)

        if not os.path.exists(backup_path):
            flash('النسخة الاحتياطية غير موجودة', 'warning')
            return redirect(url_for('settings'))

        # التحقق من صحة ملف النسخة الاحتياطية
        try:
            conn = sqlite3.connect(backup_path)
            conn.close()
        except sqlite3.Error:
            flash('ملف النسخة الاحتياطية ليس قاعدة بيانات SQLite صالحة', 'danger')
            return redirect(url_for('settings'))

        # إغلاق اتصالات قاعدة البيانات
        db.session.close()
        db.engine.dispose()

        # عمل نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        current_backup_filename = f"pre_restore_backup_{timestamp}.sqlite"
        current_backup_path = os.path.join(backup_dir, current_backup_filename)

        # نسخ قاعدة البيانات الحالية
        shutil.copy2(db_path, current_backup_path)

        # استعادة النسخة الاحتياطية
        shutil.copy2(backup_path, db_path)

        flash('تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        # طباعة تفاصيل الخطأ للتصحيح
        import traceback
        print(traceback.format_exc())

    return redirect(url_for('settings'))

# استعادة نسخة احتياطية من ملف مرفوع
@app.route('/backup/restore/upload', methods=['POST'])
def restore_backup_upload():
    try:
        if 'backup_file' not in request.files:
            flash('لم يتم تحديد ملف', 'warning')
            return redirect(url_for('settings'))

        backup_file = request.files['backup_file']

        if backup_file.filename == '':
            flash('لم يتم تحديد ملف', 'warning')
            return redirect(url_for('settings'))

        if not (backup_file.filename.endswith('.sqlite') or
                backup_file.filename.endswith('.db') or
                backup_file.filename.endswith('.backup')):
            flash('صيغة الملف غير صالحة. يجب أن يكون الملف بصيغة .sqlite أو .db أو .backup', 'warning')
            return redirect(url_for('settings'))

        # الحصول على مسار قاعدة البيانات الحالية
        db_uri = app.config['SQLALCHEMY_DATABASE_URI']

        # طباعة مسار قاعدة البيانات للتصحيح
        print(f"Database URI: {db_uri}")

        # التعامل مع مسارات قاعدة البيانات المختلفة
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
            # إذا كان المسار نسبيًا، قم بتحويله إلى مسار مطلق
            if not os.path.isabs(db_path):
                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), db_path)
        else:
            flash('نوع قاعدة البيانات غير مدعوم للاستعادة', 'danger')
            return redirect(url_for('settings'))

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # إغلاق اتصالات قاعدة البيانات
        db.session.close()
        db.engine.dispose()

        # عمل نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        current_backup_filename = f"pre_restore_backup_{timestamp}.sqlite"
        current_backup_path = os.path.join(backup_dir, current_backup_filename)

        # نسخ قاعدة البيانات الحالية
        shutil.copy2(db_path, current_backup_path)

        # حفظ الملف المرفوع مؤقتاً
        uploaded_filename = secure_filename(backup_file.filename)
        uploaded_path = os.path.join(backup_dir, f"uploaded_{timestamp}_{uploaded_filename}")
        backup_file.save(uploaded_path)

        # التحقق من صحة ملف قاعدة البيانات
        try:
            conn = sqlite3.connect(uploaded_path)
            conn.close()
        except sqlite3.Error:
            os.remove(uploaded_path)
            flash('الملف المرفوع ليس قاعدة بيانات SQLite صالحة', 'danger')
            return redirect(url_for('settings'))

        # استعادة النسخة الاحتياطية
        shutil.copy2(uploaded_path, db_path)

        # حفظ الملف المرفوع في مجلد النسخ الاحتياطية
        saved_backup_filename = f"uploaded_backup_{timestamp}_{uploaded_filename}"
        saved_backup_path = os.path.join(backup_dir, saved_backup_filename)
        shutil.copy2(uploaded_path, saved_backup_path)

        # حذف الملف المؤقت
        os.remove(uploaded_path)

        flash('تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}', 'danger')
        # طباعة تفاصيل الخطأ للتصحيح
        import traceback
        print(traceback.format_exc())

    return redirect(url_for('settings'))

# صفحة الإحصائيات
@app.route('/statistics')
def statistics():
    print("بدء تنفيذ دالة الإحصائيات")
    try:
        # إحصائيات عامة
        total_news = News.query.count()
        total_categories = Category.query.count()
        total_governorates = Governorate.query.count()
        total_sources = db.session.query(News.source).distinct().count()

        # إحصائيات الأخبار حسب التصنيف
        category_stats = []
        categories_list = Category.query.all()
        for cat in categories_list:
            news_count = News.query.filter_by(category_id=cat.id).count()
            if news_count > 0:
                category_stats.append({
                    'id': cat.id,
                    'name': cat.name,
                    'news_count': news_count
                })
        # ترتيب التصنيفات حسب عدد الأخبار تنازلياً
        category_stats = sorted(category_stats, key=lambda x: x['news_count'], reverse=True)

        # إحصائيات الأخبار حسب المحافظة
        governorate_stats = []
        governorates_list = Governorate.query.all()
        for gov in governorates_list:
            news_count = News.query.filter_by(governorate_id=gov.id).count()
            if news_count > 0:
                governorate_stats.append({
                    'id': gov.id,
                    'name': gov.name,
                    'news_count': news_count
                })
        # ترتيب المحافظات حسب عدد الأخبار تنازلياً
        governorate_stats = sorted(governorate_stats, key=lambda x: x['news_count'], reverse=True)

        # إحصائيات الأخبار حسب المصدر
        source_counts = {}
        all_news = News.query.all()
        for news in all_news:
            if news.source in source_counts:
                source_counts[news.source] += 1
            else:
                source_counts[news.source] = 1

        # تحويل القاموس إلى قائمة وترتيبها تنازلياً
        source_stats = []
        for source, count in sorted(source_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            source_stats.append({
                'source': source,
                'news_count': count
            })

        # إحصائيات الأخبار حسب التاريخ (آخر 30 يوم)
        thirty_days_ago = date.today() - timedelta(days=30)
        date_stats = []
        date_counts = {}
        recent_news = News.query.filter(News.date >= thirty_days_ago).all()
        for news in recent_news:
            date_str = news.date.strftime('%Y-%m-%d')
            if date_str in date_counts:
                date_counts[date_str] += 1
            else:
                date_counts[date_str] = 1

        # تحويل القاموس إلى قائمة وترتيبها تنازلياً حسب التاريخ
        for date_str, count in sorted(date_counts.items(), reverse=True):
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            date_stats.append({
                'date': date_obj,
                'news_count': count
            })

        # إحصائيات الحقول الديناميكية لكل تصنيف
        field_stats = []

        # الحصول على جميع الأخبار مع التصنيفات والحقول الديناميكية
        all_news = News.query.all()
        print(f"عدد الأخبار: {len(all_news)}")

        # إنشاء قاموس لتجميع البيانات حسب التصنيف
        category_data = {}

        # تجميع البيانات من جميع الأخبار
        for news in all_news:
            if news.category_id not in category_data:
                category = Category.query.get(news.category_id)
                if not category:
                    continue

                category_data[news.category_id] = {
                    'category_id': news.category_id,
                    'category_name': category.name,
                    'news_count': 0,
                    'fields': {}
                }

            # زيادة عدد الأخبار لهذا التصنيف
            category_data[news.category_id]['news_count'] += 1

            # الحصول على قيم الحقول الديناميكية لهذا الخبر
            field_values = FieldValue.query.filter_by(news_id=news.id).all()
            print(f"الخبر ID: {news.id}, عدد قيم الحقول: {len(field_values)}")

            for fv in field_values:
                field = Field.query.get(fv.field_id)
                if not field:
                    print(f"لم يتم العثور على الحقل ID: {fv.field_id}")
                    continue

                if fv.field_id not in category_data[news.category_id]['fields']:
                    category_data[news.category_id]['fields'][fv.field_id] = {
                        'id': fv.field_id,
                        'name': field.name,
                        'field_type': field.field_type,
                        'total': 0,
                        'values': {}
                    }

                # إضافة القيمة إلى قاموس القيم
                if fv.value not in category_data[news.category_id]['fields'][fv.field_id]['values']:
                    category_data[news.category_id]['fields'][fv.field_id]['values'][fv.value] = 0

                category_data[news.category_id]['fields'][fv.field_id]['values'][fv.value] += 1

                # إذا كان الحقل رقمي، نقوم بجمع القيم
                if field.field_type == 'number' and fv.value.isdigit():
                    category_data[news.category_id]['fields'][fv.field_id]['total'] += int(fv.value)

        # تحويل البيانات إلى الصيغة المطلوبة للعرض
        for category_id, data in category_data.items():
            field_data = []

            for field_id, field_info in data['fields'].items():
                # تحويل قاموس القيم إلى قائمة
                values_list = []
                for value, count in sorted(field_info['values'].items(), key=lambda x: x[1], reverse=True):
                    values_list.append({
                        'value': value,
                        'value_count': count
                    })

                # إضافة الحقل مع قائمة القيم
                field_data.append({
                    'id': field_info['id'],
                    'name': field_info['name'],
                    'field_type': field_info['field_type'],
                    'total': field_info['total'],
                    'value_items': values_list
                })

            # إضافة التصنيف مع قائمة الحقول
            field_stats.append({
                'category_id': data['category_id'],
                'category_name': data['category_name'],
                'news_count': data['news_count'],
                'fields': field_data
            })

        print(f"عدد التصنيفات في الإحصائيات: {len(field_stats)}")

        # إحصائيات الأخبار حسب الشهر والسنة
        month_stats = []
        month_counts = {}
        for news in all_news:
            month_key = f"{news.date.year}-{news.date.month:02d}"
            if month_key in month_counts:
                month_counts[month_key] += 1
            else:
                month_counts[month_key] = 1

        # تحويل القاموس إلى قائمة وترتيبها تنازلياً
        for month, count in sorted(month_counts.items(), reverse=True)[:12]:
            month_stats.append({
                'month': month,
                'news_count': count
            })
    except Exception as e:
        import traceback
        print(f"خطأ في صفحة الإحصائيات: {str(e)}")
        print(traceback.format_exc())
        # إرجاع قيم افتراضية في حالة حدوث خطأ
        total_news = 0
        total_categories = 0
        total_governorates = 0
        total_sources = 0
        category_stats = []
        governorate_stats = []
        source_stats = []
        date_stats = []
        field_stats = []
        month_stats = []

    return render_template('statistics.html',
                          total_news=total_news,
                          total_categories=total_categories,
                          total_governorates=total_governorates,
                          total_sources=total_sources,
                          category_stats=category_stats,
                          governorate_stats=governorate_stats,
                          source_stats=source_stats,
                          date_stats=date_stats,
                          field_stats=field_stats,
                          month_stats=month_stats)

# صفحة طباعة إحصائيات الحقول الديناميكية
@app.route('/stats')
def print_statistics():
    try:
        # الحصول على التاريخ الحالي بالعربية
        today = date.today()
        arabic_months = {
            1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل", 5: "مايو", 6: "يونيو",
            7: "يوليو", 8: "أغسطس", 9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
        }
        arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
        day_name = arabic_days[today.weekday()]
        today_date = f"{day_name} {today.day} {arabic_months[today.month]} {today.year}"

        # إحصائيات الحقول الديناميكية لكل تصنيف
        field_stats = []

        # الحصول على جميع الأخبار مع التصنيفات والحقول الديناميكية
        all_news = News.query.all()

        # إنشاء قاموس لتجميع البيانات حسب التصنيف
        category_data = {}

        # تجميع البيانات من جميع الأخبار
        for news in all_news:
            if news.category_id not in category_data:
                category = Category.query.get(news.category_id)
                if not category:
                    continue

                category_data[news.category_id] = {
                    'category_id': news.category_id,
                    'category_name': category.name,
                    'news_count': 0,
                    'fields': {}
                }

            # زيادة عدد الأخبار لهذا التصنيف
            category_data[news.category_id]['news_count'] += 1

            # الحصول على قيم الحقول الديناميكية لهذا الخبر
            field_values = FieldValue.query.filter_by(news_id=news.id).all()

            for fv in field_values:
                field = Field.query.get(fv.field_id)
                if not field:
                    continue

                if fv.field_id not in category_data[news.category_id]['fields']:
                    category_data[news.category_id]['fields'][fv.field_id] = {
                        'id': fv.field_id,
                        'name': field.name,
                        'field_type': field.field_type,
                        'total': 0,
                        'values': {}
                    }

                # إضافة القيمة إلى قاموس القيم
                if fv.value not in category_data[news.category_id]['fields'][fv.field_id]['values']:
                    category_data[news.category_id]['fields'][fv.field_id]['values'][fv.value] = 0

                category_data[news.category_id]['fields'][fv.field_id]['values'][fv.value] += 1

                # إذا كان الحقل رقمي، نقوم بجمع القيم
                if field.field_type == 'number' and fv.value.isdigit():
                    category_data[news.category_id]['fields'][fv.field_id]['total'] += int(fv.value)

        # تحويل البيانات إلى الصيغة المطلوبة للعرض
        for category_id, data in category_data.items():
            field_data = []

            for field_id, field_info in data['fields'].items():
                # تحويل قاموس القيم إلى قائمة
                values_list = []
                for value, count in sorted(field_info['values'].items(), key=lambda x: x[1], reverse=True):
                    values_list.append({
                        'value': value,
                        'value_count': count
                    })

                # إضافة الحقل مع قائمة القيم
                field_data.append({
                    'id': field_info['id'],
                    'name': field_info['name'],
                    'field_type': field_info['field_type'],
                    'total': field_info['total'],
                    'value_items': values_list
                })

            # إضافة التصنيف مع قائمة الحقول
            field_stats.append({
                'category_id': data['category_id'],
                'category_name': data['category_name'],
                'news_count': data['news_count'],
                'fields': field_data
            })

        # إضافة عنوان الصفحة بدون الزوائد
        response = make_response(render_template('print_statistics.html',
                                field_stats=field_stats,
                                today_date=today_date))
        # إضافة رؤوس HTTP لمنع إظهار الزوائد في عنوان الصفحة
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['Content-Security-Policy'] = "frame-ancestors 'none'"
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
    except Exception as e:
        print(f"خطأ في صفحة طباعة الإحصائيات: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('statistics'))

if __name__ == '__main__':
    app.run(debug=True)
