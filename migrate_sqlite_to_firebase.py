"""
نقل البيانات من SQLite إلى Firebase
"""

import sqlite3
import os
from datetime import datetime, date

def migrate_sqlite_to_firebase():
    """نقل البيانات من SQLite إلى Firebase"""
    
    print("🚀 بدء نقل البيانات من SQLite إلى Firebase...")
    
    # التحقق من وجود قاعدة البيانات المحلية
    if not os.path.exists('news.db'):
        print("❌ ملف news.db غير موجود!")
        return False
    
    try:
        # استيراد نماذج Firebase
        from firebase_models import Category, News, ReportArchive
        from firebase_config import test_connection
        
        # اختبار الاتصال بـ Firebase
        if not test_connection():
            print("❌ فشل الاتصال بـ Firebase")
            return False
        
        # الاتصال بقاعدة بيانات SQLite
        conn = sqlite3.connect('news.db')
        cursor = conn.cursor()
        
        print("\n📊 فحص البيانات في SQLite...")
        
        # فحص التصنيفات
        cursor.execute("SELECT COUNT(*) FROM category")
        categories_count = cursor.fetchone()[0]
        print(f"   - التصنيفات: {categories_count}")
        
        # فحص الأخبار
        cursor.execute("SELECT COUNT(*) FROM news")
        news_count = cursor.fetchone()[0]
        print(f"   - الأخبار: {news_count}")
        
        # فحص التقارير
        cursor.execute("SELECT COUNT(*) FROM report_archive")
        reports_count = cursor.fetchone()[0]
        print(f"   - التقارير: {reports_count}")
        
        if categories_count == 0 and news_count == 0 and reports_count == 0:
            print("⚠️ لا توجد بيانات في SQLite للنقل")
            return True
        
        # 1. نقل التصنيفات
        print("\n📁 نقل التصنيفات...")
        cursor.execute("SELECT * FROM category")
        categories = cursor.fetchall()
        
        category_mapping = {}  # لربط المعرفات القديمة بالجديدة
        categories_migrated = 0
        
        for cat in categories:
            try:
                # cat = (id, name, created_at)
                cat_id, cat_name = cat[0], cat[1]
                
                # التحقق من وجود التصنيف مسبق<|im_end|>
                existing = Category.filter_by('name', cat_name, limit=1)
                if existing:
                    category_mapping[cat_id] = existing[0].id
                    print(f"   ⚠️ موجود مسبق<|im_end|>: {cat_name}")
                    continue
                
                new_category = Category.create({
                    'name': cat_name,
                    'description': f'تصنيف {cat_name}',
                    'created_at': datetime.now()
                })
                
                if new_category:
                    category_mapping[cat_id] = new_category.id
                    categories_migrated += 1
                    print(f"   ✅ {cat_name}")
                else:
                    print(f"   ❌ فشل: {cat_name}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في نقل التصنيف: {str(e)}")
        
        print(f"   📊 تم نقل {categories_migrated} تصنيف")
        
        # 2. نقل الأخبار
        print("\n📰 نقل الأخبار...")
        cursor.execute("SELECT * FROM news")
        news_items = cursor.fetchall()
        
        news_migrated = 0
        
        for news_item in news_items:
            try:
                # news = (id, title, content, source, category_id, date, created_at)
                news_id, title, content, source, category_id, news_date, created_at = news_item
                
                # تحويل التاريخ
                if isinstance(news_date, str):
                    try:
                        news_date = datetime.strptime(news_date, '%Y-%m-%d').date()
                    except:
                        news_date = date.today()
                
                # ربط التصنيف الجديد
                new_category_id = category_mapping.get(category_id, '')
                
                new_news = News.create({
                    'title': title or 'بدون عنوان',
                    'content': content or '',
                    'source': source or 'مصدر غير محدد',
                    'category_id': new_category_id,
                    'date': news_date,
                    'created_at': datetime.now()
                })
                
                if new_news:
                    news_migrated += 1
                    if news_migrated % 10 == 0:
                        print(f"   📊 تم نقل {news_migrated} خبر...")
                else:
                    print(f"   ❌ فشل في نقل: {title[:50]}...")
                    
            except Exception as e:
                print(f"   ❌ خطأ في نقل الخبر: {str(e)}")
        
        print(f"   📊 تم نقل {news_migrated} خبر")
        
        # 3. نقل التقارير المؤرشفة
        print("\n📋 نقل التقارير...")
        cursor.execute("SELECT * FROM report_archive")
        reports = cursor.fetchall()
        
        reports_migrated = 0
        
        for report in reports:
            try:
                # report = (id, filename, report_type, start_date, end_date, category_id, total_news, file_size, html_content, created_at)
                (report_id, filename, report_type, start_date, end_date, 
                 category_id, total_news, file_size, html_content, created_at) = report
                
                # تحويل التواريخ
                if isinstance(start_date, str):
                    try:
                        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                    except:
                        start_date = date.today()
                
                if isinstance(end_date, str):
                    try:
                        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                    except:
                        end_date = date.today()
                
                # ربط التصنيف الجديد
                new_category_id = category_mapping.get(category_id, '') if category_id else ''
                
                new_report = ReportArchive.create({
                    'filename': filename or 'تقرير بدون اسم',
                    'report_type': report_type or 'يومي',
                    'start_date': start_date,
                    'end_date': end_date,
                    'category_id': new_category_id,
                    'total_news': total_news or 0,
                    'file_size': file_size or 0,
                    'html_content': html_content or '',
                    'created_at': datetime.now()
                })
                
                if new_report:
                    reports_migrated += 1
                    print(f"   ✅ {filename}")
                else:
                    print(f"   ❌ فشل: {filename}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في نقل التقرير: {str(e)}")
        
        print(f"   📊 تم نقل {reports_migrated} تقرير")
        
        # إغلاق الاتصال
        conn.close()
        
        # ملخص النقل
        print("\n🎉 ملخص النقل:")
        print(f"   ✅ التصنيفات: {categories_migrated}/{categories_count}")
        print(f"   ✅ الأخبار: {news_migrated}/{news_count}")
        print(f"   ✅ التقارير: {reports_migrated}/{reports_count}")
        
        total_migrated = categories_migrated + news_migrated + reports_migrated
        total_original = categories_count + news_count + reports_count
        
        print(f"\n📊 إجمالي البيانات المنقولة: {total_migrated}/{total_original}")
        
        if total_migrated > 0:
            print("🎉 تم نقل البيانات بنجاح إلى Firebase!")
            return True
        else:
            print("⚠️ لم يتم نقل أي بيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ عام في النقل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔄 نقل البيانات من SQLite إلى Firebase")
    print("=" * 60)
    
    success = migrate_sqlite_to_firebase()
    
    if success:
        print("\n✅ تم النقل بنجاح! يمكنك الآن استخدام التطبيق مع Firebase.")
    else:
        print("\n❌ فشل النقل. راجع الأخطاء أعلاه.")
    
    print("=" * 60)
