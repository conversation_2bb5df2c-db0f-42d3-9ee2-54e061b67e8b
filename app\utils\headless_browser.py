"""
وحدة المتصفح بدون واجهة (Headless Browser) باستخدام Playwright
تستخدم للتعامل مع المواقع الديناميكية التي تعتمد على JavaScript
"""
import asyncio
import logging
import random
import time
from datetime import datetime, date, timedelta
import re
import hashlib
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Optional, Tuple, Any

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from fake_useragent import UserAgent
from bs4 import BeautifulSoup
import html_text

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('headless_browser')

# قائمة الكلمات المفتاحية للعراق (نفس القائمة من news_scraper.py)
from app.utils.news_scraper import IRAQ_KEYWORDS

class HeadlessBrowser:
    """فئة المتصفح بدون واجهة للتعامل مع المواقع الديناميكية"""

    def __init__(self, source, headless=True):
        """
        تهيئة المتصفح بدون واجهة

        :param source: كائن مصدر الأخبار (NewsSource)
        :param headless: تشغيل المتصفح بدون واجهة (True) أو مع واجهة (False)
        """
        self.source = source
        self.headless = headless
        self.user_agent = UserAgent().random
        self.browser = None
        self.context = None
        self.page = None

    async def __aenter__(self):
        """دالة الدخول للسياق التزامني"""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """دالة الخروج للسياق التزامني"""
        await self.close()

    async def start(self):
        """بدء تشغيل المتصفح"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=self.headless)

            # إعداد سياق المتصفح مع عنوان المستخدم وإعدادات أخرى
            self.context = await self.browser.new_context(
                user_agent=self.user_agent,
                viewport={'width': 1920, 'height': 1080},
                locale='ar-IQ',  # استخدام اللغة العربية (العراق)
                timezone_id='Asia/Baghdad',  # استخدام توقيت بغداد
                geolocation={'latitude': 33.3152, 'longitude': 44.3661},  # إحداثيات بغداد
                permissions=['geolocation'],
                java_script_enabled=True,
            )

            # إعداد معالجة ملفات تعريف الارتباط
            await self.context.add_cookies([{
                'name': 'accept_cookies',
                'value': 'true',
                'domain': urlparse(self.source.url).netloc,
                'path': '/',
            }])

            # إنشاء صفحة جديدة
            self.page = await self.context.new_page()

            # إعداد معالجة الحوارات
            self.page.on('dialog', lambda dialog: asyncio.create_task(dialog.accept()))

            # إعداد اعتراض الطلبات لتسريع التحميل
            await self.page.route('**/*.{png,jpg,jpeg,gif,svg,ico,woff,woff2,ttf,eot}',
                                 lambda route: asyncio.create_task(route.abort()))

            logger.info(f"تم بدء تشغيل المتصفح بنجاح")
            return self

        except Exception as e:
            logger.error(f"خطأ في بدء تشغيل المتصفح: {str(e)}")
            if self.browser:
                await self.browser.close()
            raise

    async def close(self):
        """
        إغلاق المتصفح وتنظيف الموارد
        """
        try:
            # إغلاق الصفحة أولاً
            if self.page:
                try:
                    await self.page.close()
                except:
                    pass
                self.page = None

            # ثم إغلاق السياق
            if self.context:
                try:
                    await self.context.close()
                except:
                    pass
                self.context = None

            # ثم إغلاق المتصفح
            if self.browser:
                try:
                    await self.browser.close()
                except:
                    pass
                self.browser = None

            # تنظيف المهام المعلقة
            try:
                tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
                for task in tasks:
                    if not task.done():
                        task.cancel()
            except:
                pass

            logger.info("تم إغلاق المتصفح وتنظيف الموارد")
        except Exception as e:
            logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")

    async def navigate(self, url, wait_for_load=True, timeout=15000):
        """
        الانتقال إلى صفحة ويب

        :param url: عنوان URL للصفحة
        :param wait_for_load: انتظار اكتمال تحميل الصفحة
        :param timeout: مهلة الانتظار بالمللي ثانية
        :return: كائن BeautifulSoup
        """
        try:
            # إضافة معلمات عشوائية لتجنب التخزين المؤقت
            if '?' in url:
                url += f'&_={int(time.time() * 1000)}'
            else:
                url += f'?_={int(time.time() * 1000)}'

            # الانتقال إلى الصفحة بمهلة أقصر
            response = await self.page.goto(url, timeout=timeout, wait_until='domcontentloaded')

            if wait_for_load:
                # انتظار اكتمال تحميل الصفحة بمهلة أقصر
                try:
                    await self.page.wait_for_load_state('networkidle', timeout=5000)
                except:
                    # إذا انتهت المهلة، استمر على أي حال
                    pass

                # انتظار ظهور عناصر مهمة بمهلة أقصر
                selectors_to_wait = ['h1', 'article', '.article', '.content', '.news', 'main']
                for selector in selectors_to_wait:
                    try:
                        await self.page.wait_for_selector(selector, timeout=2000)
                        break
                    except:
                        continue

            # التمرير السريع لأسفل لتحميل المحتوى الكسول
            await self.scroll_page(scroll_delay=50)

            # الحصول على HTML الكامل بعد تنفيذ JavaScript
            html_content = await self.page.content()

            return BeautifulSoup(html_content, 'lxml')

        except Exception as e:
            logger.error(f"خطأ في الانتقال إلى {url}: {str(e)}")
            return None

    async def scroll_page(self, scroll_delay=50):
        """
        التمرير لأسفل الصفحة لتحميل المحتوى الكسول

        :param scroll_delay: تأخير بين عمليات التمرير بالمللي ثانية
        """
        try:
            # الحصول على ارتفاع الصفحة
            height = await self.page.evaluate('document.body.scrollHeight')

            # التمرير سريعًا إلى منتصف الصفحة ثم إلى نهايتها
            await self.page.evaluate(f'window.scrollTo(0, {height // 2})')
            await self.page.wait_for_timeout(scroll_delay)

            await self.page.evaluate(f'window.scrollTo(0, {height})')
            await self.page.wait_for_timeout(scroll_delay)

            # التمرير للأعلى
            await self.page.evaluate('window.scrollTo(0, 0)')

        except Exception as e:
            logger.error(f"خطأ في التمرير: {str(e)}")

    async def extract_structured_data(self):
        """
        استخراج البيانات المهيكلة من الصفحة

        :return: قاموس بالبيانات المهيكلة
        """
        try:
            # استخراج بيانات Schema.org
            schema_data = await self.page.evaluate('''() => {
                const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'));
                return jsonLdScripts.map(script => {
                    try {
                        return JSON.parse(script.textContent);
                    } catch (e) {
                        return null;
                    }
                }).filter(data => data !== null);
            }''')

            # استخراج بيانات Open Graph
            og_data = await self.page.evaluate('''() => {
                const metaTags = Array.from(document.querySelectorAll('meta[property^="og:"]'));
                const data = {};
                metaTags.forEach(tag => {
                    const property = tag.getAttribute('property').replace('og:', '');
                    const content = tag.getAttribute('content');
                    data[property] = content;
                });
                return data;
            }''')

            # دمج البيانات
            structured_data = {
                'schema': schema_data,
                'og': og_data
            }

            return structured_data

        except Exception as e:
            logger.error(f"خطأ في استخراج البيانات المهيكلة: {str(e)}")
            return {}

    async def extract_news(self, url):
        """
        استخراج بيانات الخبر من صفحة الويب - نسخة مبسطة تركز على الأخبار العراقية اليومية

        :param url: عنوان URL لصفحة الخبر
        :return: قاموس يحتوي على بيانات الخبر أو None في حالة الفشل
        """
        try:
            # الانتقال إلى الصفحة
            soup = await self.navigate(url)
            if not soup:
                return None

            # استخراج العنوان - تبسيط العملية
            title = None

            # محاولة استخراج العنوان باستخدام المحدد إذا كان موجوداً
            if self.source.selector_title:
                title_element = soup.select_one(self.source.selector_title)
                if title_element:
                    title = title_element.get_text().strip()

            # إذا لم يتم العثور على عنوان، استخدم عنوان الصفحة أو أول عنوان h1
            if not title:
                title_element = soup.find('title') or soup.find('h1')
                if title_element:
                    title = title_element.get_text().strip()

            # إذا لم يتم العثور على عنوان، تجاهل الخبر
            if not title:
                logger.warning(f"لم يتم العثور على عنوان للخبر: {url}")
                return None

            # استخراج محتوى الخبر - تبسيط العملية
            content = None

            # محاولة استخراج المحتوى باستخدام المحدد إذا كان موجوداً
            if self.source.selector_content:
                content_element = soup.select_one(self.source.selector_content)
                if content_element:
                    content = content_element.get_text().strip()

            # إذا لم يتم العثور على محتوى، استخدم أول فقرة أو كل الفقرات
            if not content:
                paragraphs = soup.find_all('p')
                if paragraphs:
                    content = '\n'.join([p.get_text().strip() for p in paragraphs if p.get_text().strip()])

            # إذا لم يتم العثور على محتوى، تجاهل الخبر
            if not content:
                logger.warning(f"لم يتم العثور على محتوى للخبر: {url}")
                return None

            # استخدام تاريخ اليوم مباشرة
            news_date = date.today()

            # التحقق السريع من أن الخبر يتعلق بالعراق
            if not self._is_related_to_iraq(title, content):
                logger.debug(f"تم تجاهل الخبر لأنه غير متعلق بالعراق: {title}")
                return None

            # إنشاء بصمة المحتوى
            content_hash = self._create_content_hash(title, content)

            return {
                'title': title,
                'content': content,
                'date': news_date,
                'source': self.source.name,
                'source_url': url,
                'freshness': "اليوم",
                'iraq_related': True,
                'content_hash': content_hash
            }

        except Exception as e:
            logger.error(f"خطأ في استخراج الخبر من {url}: {str(e)}")
            return None

    def _extract_title_from_structured_data(self, structured_data):
        """استخراج العنوان من البيانات المهيكلة"""
        # محاولة استخراج العنوان من بيانات Open Graph
        if 'og' in structured_data and 'title' in structured_data['og']:
            return structured_data['og']['title']

        # محاولة استخراج العنوان من بيانات Schema.org
        if 'schema' in structured_data and structured_data['schema']:
            for item in structured_data['schema']:
                if isinstance(item, dict):
                    if '@type' in item and item['@type'] in ['NewsArticle', 'Article', 'BlogPosting']:
                        if 'headline' in item:
                            return item['headline']
                        elif 'name' in item:
                            return item['name']

        return None

    def _extract_content_from_structured_data(self, structured_data):
        """استخراج المحتوى من البيانات المهيكلة"""
        # محاولة استخراج المحتوى من بيانات Schema.org
        if 'schema' in structured_data and structured_data['schema']:
            for item in structured_data['schema']:
                if isinstance(item, dict):
                    if '@type' in item and item['@type'] in ['NewsArticle', 'Article', 'BlogPosting']:
                        if 'articleBody' in item:
                            return item['articleBody']
                        elif 'description' in item:
                            return item['description']

        # محاولة استخراج المحتوى من بيانات Open Graph
        if 'og' in structured_data and 'description' in structured_data['og']:
            return structured_data['og']['description']

        return None

    def _extract_date_from_structured_data(self, structured_data):
        """استخراج التاريخ من البيانات المهيكلة"""
        # محاولة استخراج التاريخ من بيانات Schema.org
        if 'schema' in structured_data and structured_data['schema']:
            for item in structured_data['schema']:
                if isinstance(item, dict):
                    if '@type' in item and item['@type'] in ['NewsArticle', 'Article', 'BlogPosting']:
                        for date_field in ['datePublished', 'dateCreated', 'dateModified']:
                            if date_field in item:
                                try:
                                    date_str = item[date_field]
                                    # تحويل التاريخ من صيغة ISO 8601
                                    if 'T' in date_str:
                                        date_str = date_str.split('T')[0]
                                    return datetime.strptime(date_str, '%Y-%m-%d').date()
                                except:
                                    pass

        return None

    def _parse_date(self, date_text):
        """
        تحليل نص التاريخ واستخراج كائن تاريخ

        :param date_text: نص التاريخ
        :return: كائن date أو None في حالة الفشل
        """
        # استخدام نفس دالة parse_date من news_scraper.py
        from app.utils.news_scraper import NewsScraper
        scraper = NewsScraper(self.source)
        return scraper.parse_date(date_text)

    def _is_related_to_iraq(self, title, content):
        """
        التحقق مما إذا كان الخبر يتعلق بالعراق

        :param title: عنوان الخبر
        :param content: محتوى الخبر
        :return: True إذا كان الخبر يتعلق بالعراق، False خلاف ذلك
        """
        # استخدام نفس دالة is_related_to_iraq من news_scraper.py
        from app.utils.news_scraper import NewsScraper
        scraper = NewsScraper(self.source)
        return scraper.is_related_to_iraq(title, content)

    def _create_content_hash(self, title, content):
        """
        إنشاء بصمة المحتوى (Content Hash)

        :param title: عنوان الخبر
        :param content: محتوى الخبر
        :return: بصمة المحتوى
        """
        # إنشاء نص للبصمة من العنوان والمحتوى
        hash_text = f"{title}|{content}"

        # إنشاء بصمة MD5
        return hashlib.md5(hash_text.encode('utf-8')).hexdigest()

    async def scrape(self, max_pages=2, max_news=5):
        """
        الزحف واستخراج الأخبار من المصدر

        :param max_pages: الحد الأقصى لعدد الصفحات للزحف
        :param max_news: الحد الأقصى لعدد الأخبار للاستخراج
        :return: قائمة بالأخبار المستخرجة
        """
        news_list = []
        visited_urls = set()
        urls_to_visit = [self.source.url]

        # إضافة أقسام الأخبار المحلية والعراقية إذا كانت متوفرة
        common_sections = [
            '/iraq', '/local', '/news/iraq', '/news/local', '/category/iraq', '/category/local',
            '/ar/iraq', '/ar/local', '/ar/news/iraq', '/ar/news/local',
            '/العراق', '/محلي', '/محليات', '/أخبار-العراق', '/أخبار-محلية'
        ]

        # إضافة فقط أقسام الأخبار الأكثر صلة
        for section in common_sections[:5]:  # استخدام أول 5 أقسام فقط
            section_url = urljoin(self.source.url, section)
            if section_url not in urls_to_visit and section_url != self.source.url:
                urls_to_visit.append(section_url)

        # عدد المحاولات الفاشلة المتتالية
        consecutive_failures = 0
        max_consecutive_failures = 3  # تقليل عدد المحاولات

        # تحديد وقت بدء الزحف
        start_time = time.time()
        max_crawl_time = 60  # الحد الأقصى للزحف هو 60 ثانية

        while urls_to_visit and len(visited_urls) < max_pages and len(news_list) < max_news:
            # التحقق من الوقت المنقضي
            if time.time() - start_time > max_crawl_time:
                logger.warning(f"تم تجاوز الحد الأقصى لوقت الزحف ({max_crawl_time} ثانية)")
                break

            # أخذ عنوان URL من القائمة
            current_url = urls_to_visit.pop(0)

            # تجنب زيارة نفس العنوان مرتين
            if current_url in visited_urls:
                continue

            logger.info(f"جاري زيارة: {current_url}")
            visited_urls.add(current_url)

            # جلب الصفحة
            soup = await self.navigate(current_url)
            if not soup:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logger.warning(f"تم تجاوز الحد الأقصى للمحاولات الفاشلة المتتالية ({max_consecutive_failures})")
                    break
                continue

            # إعادة تعيين عداد المحاولات الفاشلة
            consecutive_failures = 0

            # التحقق مما إذا كانت الصفحة الحالية هي صفحة خبر
            news = await self.extract_news(current_url)
            if news:
                # التحقق من عدم وجود خبر مشابه
                is_duplicate = False
                for existing_news in news_list:
                    if existing_news['content_hash'] == news['content_hash']:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    news_list.append(news)
                    logger.info(f"تم استخراج الخبر: {news['title']}")

            # استخراج روابط جديدة فقط إذا لم نصل إلى الحد الأقصى من الأخبار
            if len(news_list) < max_news:
                # استخراج روابط جديدة (فقط الروابط التي تبدو كروابط أخبار)
                links = []
                for a in soup.find_all('a', href=True):
                    href = a['href']
                    # تحويل الروابط النسبية إلى روابط مطلقة
                    full_url = urljoin(self.source.url, href)
                    # التحقق من أن الرابط ينتمي لنفس الموقع
                    if urlparse(full_url).netloc == urlparse(self.source.url).netloc:
                        # التحقق من أن الرابط يبدو كرابط خبر
                        if any(keyword in href.lower() for keyword in ['news', 'article', 'post', 'story', 'خبر', 'مقال']):
                            links.append(full_url)

                # إضافة الروابط الجديدة (بحد أقصى 10 روابط)
                for link in links[:10]:
                    if link not in visited_urls and link not in urls_to_visit:
                        urls_to_visit.append(link)
            # تقليل وقت الانتظار بين الطلبات
            await self.page.wait_for_timeout(random.uniform(500, 1000))

        logger.info(f"تم استخراج {len(news_list)} خبر من {self.source.name}")
        return news_list


# دالة مساعدة للاستخدام في app.py
async def scrape_with_headless_browser(source, max_pages=2, max_news=5):
    """
    دالة مساعدة لاستخدام المتصفح بدون واجهة

    :param source: كائن مصدر الأخبار (NewsSource)
    :param max_pages: الحد الأقصى لعدد الصفحات للزحف
    :param max_news: الحد الأقصى لعدد الأخبار للاستخراج
    :return: قائمة بالأخبار المستخرجة
    """
    async with HeadlessBrowser(source) as browser:
        return await browser.scrape(max_pages, max_news)
