"""
وحدة الكشف التلقائي عن محددات CSS وإعدادات مصادر الأخبار
"""
import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, date
import logging
import time
import random
from urllib.parse import urlparse, urljoin

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('auto_detector')

# قائمة الكلمات المفتاحية للعراق
IRAQ_KEYWORDS = [
    'العراق', 'عراقي', 'عراقية', 'بغداد', 'البصرة', 'الموصل', 'نينوى', 'أربيل', 'النجف',
    'كربلاء', 'ذي قار', 'الأنبار', 'ديالى', 'واسط', 'ميسان', 'المثنى', 'صلاح الدين',
    'بابل', 'القادسية', 'كركوك', 'دهوك', 'السليمانية'
]

# قائمة محددات CSS الشائعة لعناوين الأخبار
COMMON_TITLE_SELECTORS = [
    'h1', 'h1.title', 'h1.entry-title', 'h1.post-title', 'h1.article-title',
    '.article-title', '.post-title', '.entry-title', '.title', '.headline',
    'article h1', '.news-title', '.story-title', '.main-title'
]

# قائمة محددات CSS الشائعة لمحتوى الأخبار
COMMON_CONTENT_SELECTORS = [
    'article', '.article-content', '.post-content', '.entry-content', '.content',
    '.story-content', '.news-content', '.article-body', '.post-body', '.entry-body',
    '.story-body', '.news-body', '.article-text', '.post-text', '.entry-text',
    '.story-text', '.news-text', 'main'
]

# قائمة محددات CSS الشائعة لتاريخ الأخبار
COMMON_DATE_SELECTORS = [
    '.date', '.time', '.timestamp', '.article-date', '.post-date', '.entry-date',
    '.story-date', '.news-date', '.article-time', '.post-time', '.entry-time',
    '.story-time', '.news-time', '.published', '.published-date', '.pub-date',
    'time', '[datetime]', '.date-published', '.meta-date'
]

class AutoDetector:
    """فئة للكشف التلقائي عن محددات CSS وإعدادات مصادر الأخبار"""

    def __init__(self, url, user_agent=None):
        """
        تهيئة الكاشف التلقائي

        :param url: عنوان URL للموقع
        :param user_agent: عنوان المستخدم للطلبات HTTP
        """
        self.url = url

        # قائمة عناوين المستخدم المختلفة للتناوب بينها
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
        ]

        self.user_agent = user_agent or self.user_agents[0]
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        })

    def fetch_page(self, url, max_retries=3, timeout=20):
        """
        جلب صفحة ويب

        :param url: عنوان URL للصفحة
        :param max_retries: عدد المحاولات القصوى
        :param timeout: مهلة الاتصال بالثواني
        :return: كائن BeautifulSoup
        """
        for retry in range(max_retries):
            try:
                # تغيير عنوان المستخدم في كل محاولة
                if retry > 0:
                    import random
                    new_user_agent = random.choice(self.user_agents)
                    self.session.headers.update({'User-Agent': new_user_agent})
                    logger.info(f"تغيير عنوان المستخدم في المحاولة {retry+1}")
                    # إضافة تأخير قبل إعادة المحاولة
                    time.sleep(random.uniform(2, 5))

                # إضافة معلمات عشوائية لتجنب التخزين المؤقت
                params = {}
                if retry > 0:
                    params['_'] = str(int(time.time() * 1000))

                response = self.session.get(url, timeout=timeout, params=params)

                # التحقق من الاستجابة
                if response.status_code == 403:
                    logger.warning(f"تم رفض الوصول (403) لـ {url}، محاولة تجاوز...")
                    continue

                response.raise_for_status()

                # التحقق من محتوى الصفحة
                if len(response.content) < 500:
                    logger.warning(f"محتوى الصفحة {url} قصير جداً، قد يكون تم حظر الوصول")
                    if retry < max_retries - 1:
                        continue

                return BeautifulSoup(response.content, 'lxml')

            except requests.exceptions.Timeout:
                logger.warning(f"انتهت مهلة الاتصال بـ {url} في المحاولة {retry+1}/{max_retries}")
                if retry < max_retries - 1:
                    continue

            except requests.exceptions.HTTPError as e:
                logger.error(f"خطأ HTTP في جلب الصفحة {url}: {str(e)}")
                if retry < max_retries - 1:
                    continue

            except Exception as e:
                logger.error(f"خطأ في جلب الصفحة {url}: {str(e)}")
                if retry < max_retries - 1:
                    continue

        logger.error(f"فشلت جميع محاولات جلب الصفحة {url}")
        return None

    def extract_links(self, soup, base_url):
        """
        استخراج روابط الأخبار من صفحة الويب

        :param soup: كائن BeautifulSoup
        :param base_url: عنوان URL الأساسي
        :return: قائمة بروابط الأخبار
        """
        links = []
        for a in soup.find_all('a', href=True):
            href = a['href']
            # تحويل الروابط النسبية إلى روابط مطلقة
            full_url = urljoin(base_url, href)
            # التحقق من أن الرابط ينتمي لنفس الموقع
            if urlparse(full_url).netloc == urlparse(base_url).netloc:
                links.append(full_url)

        return list(set(links))  # إزالة الروابط المكررة

    def is_news_page(self, url, soup):
        """
        التحقق مما إذا كانت الصفحة هي صفحة خبر

        :param url: عنوان URL للصفحة
        :param soup: كائن BeautifulSoup
        :return: True إذا كانت صفحة خبر، False خلاف ذلك
        """
        # التحقق من وجود عنوان كبير (h1)
        if not soup.find('h1'):
            return False

        # التحقق من وجود محتوى نصي كبير
        text_content = soup.get_text()
        if len(text_content.split()) < 100:  # أقل من 100 كلمة
            return False

        # التحقق من وجود تاريخ
        date_patterns = [
            r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # مثل 01/01/2023
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',    # مثل 2023/01/01
            r'\d{1,2}\s+[يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر]\s+\d{4}'  # مثل 1 يناير 2023
        ]

        for pattern in date_patterns:
            if re.search(pattern, text_content):
                return True

        # التحقق من وجود كلمات مفتاحية للأخبار
        news_keywords = ['خبر', 'أخبار', 'مقال', 'تقرير', 'صحافة', 'صحفي', 'مراسل', 'وكالة', 'جريدة', 'صحيفة']
        for keyword in news_keywords:
            if keyword in text_content:
                return True

        return False

    def detect_title_selector(self, soup):
        """
        اكتشاف محدد CSS لعنوان الخبر

        :param soup: كائن BeautifulSoup
        :return: محدد CSS للعنوان
        """
        for selector in COMMON_TITLE_SELECTORS:
            element = soup.select_one(selector)
            if element and element.get_text().strip():
                return selector

        # إذا لم يتم العثور على محدد من القائمة، ابحث عن أول عنوان h1
        h1 = soup.find('h1')
        if h1:
            # محاولة إنشاء محدد CSS أكثر تحديداً
            classes = h1.get('class', [])
            if classes:
                return f"h1.{' '.join(classes)}"

            # محاولة العثور على العنصر الأب
            parent = h1.parent
            if parent and parent.name != 'body':
                parent_classes = parent.get('class', [])
                if parent_classes:
                    return f"{parent.name}.{' '.join(parent_classes)} h1"

            return 'h1'

        return None

    def detect_content_selector(self, soup):
        """
        اكتشاف محدد CSS لمحتوى الخبر

        :param soup: كائن BeautifulSoup
        :return: محدد CSS للمحتوى
        """
        for selector in COMMON_CONTENT_SELECTORS:
            element = soup.select_one(selector)
            if element and len(element.get_text().strip().split()) > 50:  # أكثر من 50 كلمة
                return selector

        # إذا لم يتم العثور على محدد من القائمة، ابحث عن أكبر كتلة نصية
        paragraphs = soup.find_all('p')
        if paragraphs:
            # البحث عن العنصر الأب المشترك للفقرات
            parents = {}
            for p in paragraphs:
                parent = p.parent
                if parent:
                    parent_key = f"{parent.name}.{' '.join(parent.get('class', []))}" if parent.get('class') else parent.name
                    if parent_key in parents:
                        parents[parent_key] += 1
                    else:
                        parents[parent_key] = 1

            # العثور على العنصر الأب الأكثر شيوعاً
            if parents:
                most_common_parent = max(parents.items(), key=lambda x: x[1])[0]
                return most_common_parent

        return None

    def detect_date_selector(self, soup):
        """
        اكتشاف محدد CSS لتاريخ الخبر

        :param soup: كائن BeautifulSoup
        :return: محدد CSS للتاريخ
        """
        for selector in COMMON_DATE_SELECTORS:
            element = soup.select_one(selector)
            if element and element.get_text().strip():
                # التحقق من أن النص يحتوي على تاريخ
                text = element.get_text().strip()
                date_patterns = [
                    r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # مثل 01/01/2023
                    r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',    # مثل 2023/01/01
                    r'\d{1,2}\s+[يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر]\s+\d{4}'  # مثل 1 يناير 2023
                ]

                for pattern in date_patterns:
                    if re.search(pattern, text):
                        return selector

        # البحث عن عناصر time
        time_elements = soup.find_all('time')
        if time_elements:
            return 'time'

        # البحث عن عناصر تحتوي على سمة datetime
        elements_with_datetime = soup.find_all(attrs={"datetime": True})
        if elements_with_datetime:
            return f"{elements_with_datetime[0].name}[datetime]"

        return None

    def detect_date_format(self, soup, date_selector):
        """
        اكتشاف صيغة التاريخ

        :param soup: كائن BeautifulSoup
        :param date_selector: محدد CSS للتاريخ
        :return: صيغة التاريخ
        """
        if not date_selector:
            return None

        element = soup.select_one(date_selector)
        if not element:
            return None

        text = element.get_text().strip()

        # محاولة اكتشاف صيغة التاريخ
        if re.search(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', text):
            return '%Y-%m-%d'
        elif re.search(r'\d{1,2}[-/]\d{1,2}[-/]\d{4}', text):
            return '%d-%m-%Y'
        elif re.search(r'\d{1,2}\s+[يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر]\s+\d{4}', text):
            return 'arabic_month'

        return None

    def detect_keywords(self, soup):
        """
        اكتشاف الكلمات المفتاحية

        :param soup: كائن BeautifulSoup
        :return: قائمة بالكلمات المفتاحية
        """
        # البحث عن وسوم meta للكلمات المفتاحية
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords and meta_keywords.get('content'):
            keywords = [k.strip() for k in meta_keywords.get('content').split(',')]
            return keywords

        # استخدام الكلمات المفتاحية الافتراضية للعراق
        return IRAQ_KEYWORDS

    def detect_all_fast(self):
        """
        اكتشاف جميع الإعدادات بشكل أسرع

        :return: قاموس بالإعدادات المكتشفة
        """
        # جلب الصفحة الرئيسية
        soup = self.fetch_page(self.url)
        if not soup:
            return None

        # استخراج روابط الأخبار
        links = self.extract_links(soup, self.url)

        # ترتيب الروابط حسب الأولوية (الروابط التي تحتوي على كلمات مفتاحية للعراق)
        prioritized_links = []
        normal_links = []

        for link in links:
            # التحقق من وجود كلمات مفتاحية في الرابط
            has_keyword = False
            for keyword in ['iraq', 'local', 'news', 'article', 'العراق', 'محلي', 'محليات', 'بغداد', 'خبر', 'أخبار', 'مقال']:
                if keyword in link.lower():
                    has_keyword = True
                    break

            if has_keyword:
                prioritized_links.append(link)
            else:
                normal_links.append(link)

        # دمج الروابط مع وضع الروابط ذات الأولوية في البداية
        sorted_links = prioritized_links + normal_links

        # البحث عن صفحات الأخبار
        news_pages = []
        visited_urls = set()
        max_attempts = 5  # تقليل عدد المحاولات للتسريع

        for i, link in enumerate(sorted_links[:max_attempts]):
            if link in visited_urls:
                continue

            visited_urls.add(link)
            logger.info(f"فحص الرابط {i+1}/{max_attempts}: {link}")

            page_soup = self.fetch_page(link)
            if not page_soup:
                continue

            # التحقق مما إذا كانت صفحة خبر
            if self.is_news_page(link, page_soup):
                news_pages.append((link, page_soup))
                logger.info(f"تم العثور على صفحة خبر: {link}")

            # تأخير عشوائي قصير لتجنب الحظر
            time.sleep(random.uniform(0.2, 0.5))

            if len(news_pages) >= 2:  # اكتفي بـ 2 صفحات أخبار للتسريع
                break

        if not news_pages:
            logger.error(f"لم يتم العثور على صفحات أخبار في {self.url}")
            return None

        # اكتشاف المحددات من صفحات الأخبار
        title_selectors = {}
        content_selectors = {}
        date_selectors = {}

        for link, page_soup in news_pages:
            title_selector = self.detect_title_selector(page_soup)
            if title_selector:
                title_selectors[title_selector] = title_selectors.get(title_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد العنوان: {title_selector}")

            content_selector = self.detect_content_selector(page_soup)
            if content_selector:
                content_selectors[content_selector] = content_selectors.get(content_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد المحتوى: {content_selector}")

            date_selector = self.detect_date_selector(page_soup)
            if date_selector:
                date_selectors[date_selector] = date_selectors.get(date_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد التاريخ: {date_selector}")

        # اختيار المحددات الأكثر شيوعاً
        best_title_selector = max(title_selectors.items(), key=lambda x: x[1])[0] if title_selectors else 'h1'
        best_content_selector = max(content_selectors.items(), key=lambda x: x[1])[0] if content_selectors else 'article, .article, .content, .news-content, main'
        best_date_selector = max(date_selectors.items(), key=lambda x: x[1])[0] if date_selectors else '.date, .time, time, .published, .publish-date'

        # اكتشاف صيغة التاريخ
        date_format = None
        if best_date_selector and news_pages:
            date_format = self.detect_date_format(news_pages[0][1], best_date_selector)

        # اكتشاف الكلمات المفتاحية
        keywords = self.detect_keywords(soup)

        # إضافة معلومات إضافية
        is_iraqi = False
        domain = urlparse(self.url).netloc
        if '.iq' in domain or any(keyword in domain for keyword in ['iraq', 'عراق']):
            is_iraqi = True
            logger.info(f"تم اكتشاف أن المصدر عراقي: {domain}")

        return {
            'selector_title': best_title_selector,
            'selector_content': best_content_selector,
            'selector_date': best_date_selector,
            'date_format': date_format,
            'keywords': keywords or ['العراق', 'بغداد', 'الموصل', 'البصرة'],
            'is_iraqi': is_iraqi
        }

    def detect_all(self):
        """
        اكتشاف جميع الإعدادات

        :return: قاموس بالإعدادات المكتشفة
        """
        # جلب الصفحة الرئيسية
        soup = self.fetch_page(self.url)
        if not soup:
            return None

        # استخراج روابط الأخبار
        links = self.extract_links(soup, self.url)

        # إضافة أقسام الأخبار المحلية والعراقية إذا كانت متوفرة
        common_sections = [
            '/iraq', '/local', '/news/iraq', '/news/local', '/category/iraq', '/category/local',
            '/ar/iraq', '/ar/local', '/ar/news/iraq', '/ar/news/local',
            '/العراق', '/محلي', '/محليات', '/أخبار-العراق', '/أخبار-محلية'
        ]

        for section in common_sections:
            section_url = urljoin(self.url, section)
            if section_url not in links and section_url != self.url:
                links.append(section_url)

        # ترتيب الروابط حسب الأولوية (الروابط التي تحتوي على كلمات مفتاحية للعراق)
        prioritized_links = []
        normal_links = []

        for link in links:
            # التحقق من وجود كلمات مفتاحية في الرابط
            has_keyword = False
            for keyword in ['iraq', 'local', 'news', 'article', 'العراق', 'محلي', 'محليات', 'بغداد', 'خبر', 'أخبار', 'مقال']:
                if keyword in link.lower():
                    has_keyword = True
                    break

            if has_keyword:
                prioritized_links.append(link)
            else:
                normal_links.append(link)

        # دمج الروابط مع وضع الروابط ذات الأولوية في البداية
        sorted_links = prioritized_links + normal_links

        # البحث عن صفحات الأخبار
        news_pages = []
        visited_urls = set()
        max_attempts = 15  # زيادة عدد المحاولات

        for i, link in enumerate(sorted_links[:max_attempts]):
            if link in visited_urls:
                continue

            visited_urls.add(link)
            logger.info(f"فحص الرابط {i+1}/{max_attempts}: {link}")

            page_soup = self.fetch_page(link)
            if not page_soup:
                continue

            # التحقق مما إذا كانت صفحة خبر
            if self.is_news_page(link, page_soup):
                news_pages.append((link, page_soup))
                logger.info(f"تم العثور على صفحة خبر: {link}")
            else:
                # إذا لم تكن صفحة خبر، قد تكون صفحة قائمة أخبار، استخرج الروابط منها
                sub_links = self.extract_links(page_soup, self.url)
                for sub_link in sub_links[:5]:  # فحص أول 5 روابط فرعية فقط
                    if sub_link in visited_urls:
                        continue

                    visited_urls.add(sub_link)
                    logger.info(f"فحص رابط فرعي: {sub_link}")

                    sub_soup = self.fetch_page(sub_link)
                    if sub_soup and self.is_news_page(sub_link, sub_soup):
                        news_pages.append((sub_link, sub_soup))
                        logger.info(f"تم العثور على صفحة خبر فرعية: {sub_link}")

                    # تأخير عشوائي لتجنب الحظر
                    time.sleep(random.uniform(0.5, 1.5))

                    if len(news_pages) >= 5:  # اكتفي بـ 5 صفحات أخبار
                        break

            # تأخير عشوائي لتجنب الحظر
            time.sleep(random.uniform(0.5, 1.5))

            if len(news_pages) >= 5:  # اكتفي بـ 5 صفحات أخبار
                break

        if not news_pages:
            logger.error(f"لم يتم العثور على صفحات أخبار في {self.url}")
            return None

        # اكتشاف المحددات من صفحات الأخبار
        title_selectors = {}
        content_selectors = {}
        date_selectors = {}

        for link, page_soup in news_pages:
            title_selector = self.detect_title_selector(page_soup)
            if title_selector:
                title_selectors[title_selector] = title_selectors.get(title_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد العنوان: {title_selector}")

            content_selector = self.detect_content_selector(page_soup)
            if content_selector:
                content_selectors[content_selector] = content_selectors.get(content_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد المحتوى: {content_selector}")

            date_selector = self.detect_date_selector(page_soup)
            if date_selector:
                date_selectors[date_selector] = date_selectors.get(date_selector, 0) + 1
                logger.info(f"تم اكتشاف محدد التاريخ: {date_selector}")

        # اختيار المحددات الأكثر شيوعاً
        best_title_selector = max(title_selectors.items(), key=lambda x: x[1])[0] if title_selectors else None
        best_content_selector = max(content_selectors.items(), key=lambda x: x[1])[0] if content_selectors else None
        best_date_selector = max(date_selectors.items(), key=lambda x: x[1])[0] if date_selectors else None

        # التحقق من فعالية المحددات
        if best_title_selector and best_content_selector:
            # اختبار المحددات على صفحات الأخبار
            success_count = 0
            for link, page_soup in news_pages:
                title_element = page_soup.select_one(best_title_selector)
                content_element = page_soup.select_one(best_content_selector)

                if title_element and content_element:
                    title_text = title_element.get_text().strip()
                    content_text = content_element.get_text().strip()

                    if title_text and len(content_text) > 100:  # التأكد من وجود محتوى كافٍ
                        success_count += 1

            # إذا كانت نسبة النجاح منخفضة، حاول استخدام محددات بديلة
            if success_count < len(news_pages) / 2:
                logger.warning(f"نسبة نجاح المحددات منخفضة: {success_count}/{len(news_pages)}")

                # استخدام محددات أكثر عمومية
                if best_title_selector and '.' in best_title_selector:
                    best_title_selector = best_title_selector.split('.')[0]

                if best_content_selector and '.' in best_content_selector:
                    best_content_selector = best_content_selector.split('.')[0]

        # اكتشاف صيغة التاريخ
        date_format = None
        if best_date_selector and news_pages:
            date_format = self.detect_date_format(news_pages[0][1], best_date_selector)
            logger.info(f"تم اكتشاف صيغة التاريخ: {date_format}")

        # اكتشاف الكلمات المفتاحية
        keywords = self.detect_keywords(soup)

        # إضافة معلومات إضافية
        is_iraqi = False
        domain = urlparse(self.url).netloc
        if '.iq' in domain or any(keyword in domain for keyword in ['iraq', 'عراق']):
            is_iraqi = True
            logger.info(f"تم اكتشاف أن المصدر عراقي: {domain}")

        return {
            'selector_title': best_title_selector,
            'selector_content': best_content_selector,
            'selector_date': best_date_selector,
            'date_format': date_format,
            'keywords': keywords,
            'is_iraqi': is_iraqi
        }
