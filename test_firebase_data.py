"""
اختبار قراءة البيانات من Firebase
"""

from firebase_models import Category, News, ReportArchive
from firebase_config import test_connection

def test_firebase_data():
    """اختبار قراءة البيانات من Firebase"""
    
    print("🧪 اختبار قراءة البيانات من Firebase...")
    
    # اختبار الاتصال
    if not test_connection():
        print("❌ فشل الاتصال بـ Firebase")
        return False
    
    try:
        # اختبار التصنيفات
        print("\n📁 اختبار التصنيفات...")
        categories = Category.get_all(limit=5)
        print(f"   عدد التصنيفات: {len(categories)}")
        for cat in categories[:3]:
            print(f"   - {cat.name}")
        
        # اختبار الأخبار
        print("\n📰 اختبار الأخبار...")
        news = News.get_all(limit=5)
        print(f"   عدد الأخبار: {len(news)}")
        for news_item in news[:3]:
            print(f"   - {news_item.title[:50]}...")
            print(f"     التاريخ: {news_item.date}")
            print(f"     المصدر: {news_item.source}")
        
        # اختبار التقارير
        print("\n📋 اختبار التقارير...")
        reports = ReportArchive.get_all(limit=5)
        print(f"   عدد التقارير: {len(reports)}")
        for report in reports[:3]:
            print(f"   - {report.filename}")
            print(f"     النوع: {report.report_type}")
            print(f"     تاريخ البداية: {report.start_date}")
            print(f"     تاريخ النهاية: {report.end_date}")
            print(f"     تاريخ الإنشاء: {report.created_at}")
            print(f"     عدد الأخبار: {report.total_news}")
        
        print("\n✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار البيانات في Firebase")
    print("=" * 50)
    
    success = test_firebase_data()
    
    if success:
        print("\n✅ جميع البيانات تعمل بشكل صحيح!")
    else:
        print("\n❌ هناك مشاكل في البيانات.")
    
    print("=" * 50)
