<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أرشيف التقارير - نظام الرصد الإعلامي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .archive-stats {
            background: linear-gradient(135deg, #0056b3, #007bff);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .report-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .report-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .report-type-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 15px;
        }
        .report-type-daily {
            background-color: #28a745;
            color: white;
        }
        .report-type-period {
            background-color: #007bff;
            color: white;
        }
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .month-group {
            margin-bottom: 30px;
            padding-left: 15px;
            border-left: 3px solid #007bff;
        }
        .border-left-primary {
            border-left: 4px solid #007bff !important;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='img/logo2.png') }}" alt="شعار النظام" height="40">
                نظام الرصد الإعلامي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_news') }}">الأخبار</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('print_news') }}">طباعة التقارير</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('reports_archive') }}">أرشيف التقارير</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auto_archive_page') }}">الأرشفة التلقائية</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- إحصائيات الأرشيف -->
        <div class="archive-stats">
            <h2 class="text-center mb-4">
                <i class="fas fa-archive me-2"></i>أرشيف التقارير المصدرة
            </h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ total_reports }}</h3>
                        <p><i class="fas fa-file-pdf me-2"></i>إجمالي التقارير</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ daily_reports }}</h3>
                        <p><i class="fas fa-calendar-day me-2"></i>التقارير اليومية</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ period_reports }}</h3>
                        <p><i class="fas fa-calendar-week me-2"></i>تقارير الفترات</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-center mt-3">
                    <a href="{{ url_for('auto_archive_page') }}" class="btn btn-success btn-lg">
                        <i class="fas fa-robot me-2"></i>الأرشفة التلقائية للأخبار
                    </a>
                    <p class="text-muted mt-2 mb-0">
                        <small>أرشفة تلقائية لجميع الأخبار حسب التاريخ</small>
                    </p>
                </div>
            </div>
        </div>

        <!-- إحصائيات الشهور -->
        {% if monthly_data %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات التقارير حسب الشهر</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for month_data in monthly_data %}
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card border-left-primary h-100">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    {{ month_data.month_name }} {{ month_data.year }}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    {{ month_data.count }} تقرير
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <a href="?year={{ month_data.year }}&month={{ month_data.month }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <h5><i class="fas fa-filter me-2"></i>فلترة التقارير</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="report_type" class="form-label">نوع التقرير</label>
                    <select class="form-select" id="report_type" name="report_type">
                        <option value="">جميع الأنواع</option>
                        <option value="يومي" {% if report_type == 'يومي' %}selected{% endif %}>يومي</option>
                        <option value="فترة" {% if report_type == 'فترة' %}selected{% endif %}>فترة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category_id" class="form-label">التصنيف</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">جميع التصنيفات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_id|string == category.id|string %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="year" class="form-label">السنة</label>
                    <select class="form-select" id="year" name="year">
                        <option value="">جميع السنوات</option>
                        {% for y in range(2024, 2030) %}
                        <option value="{{ y }}" {% if year|string == y|string %}selected{% endif %}>{{ y }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="month" class="form-label">الشهر</label>
                    <select class="form-select" id="month" name="month">
                        <option value="">جميع الشهور</option>
                        {% for m_num, m_name in months_arabic.items() %}
                        <option value="{{ m_num }}" {% if month|string == m_num|string %}selected{% endif %}>{{ m_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-1">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}" {% if month and year %}disabled{% endif %}>
                </div>
                <div class="col-md-1">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}" {% if month and year %}disabled{% endif %}>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- قائمة التقارير -->
        <div class="row">
            {% if reports.items %}
                {% set current_month = '' %}
                {% for report in reports.items %}
                    {% set report_month = report.created_at|strftime('%Y-%m') %}
                    {% if report_month != current_month %}
                        {% set current_month = report_month %}
                        {% if not loop.first %}
                            </div> <!-- إغلاق المجموعة السابقة -->
                        {% endif %}
                        <div class="col-12 mb-3">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-calendar-alt me-2"></i>
                                {{ months_arabic[report.created_at.month] }} {{ report.created_at.year }}
                            </h5>
                        </div>
                        <div class="month-group"> <!-- بداية المجموعة الجديدة -->
                    {% endif %}
                <div class="col-12">
                    <div class="report-card">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                    {{ report.filename }}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ report.report_period }}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <span class="report-type-badge {% if report.report_type == 'يومي' %}report-type-daily{% else %}report-type-period{% endif %}">
                                    {{ report.report_type }}
                                </span>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    <i class="fas fa-newspaper me-1"></i>
                                    {{ report.total_news }} خبر
                                </small><br>
                                <small class="text-muted">
                                    <i class="fas fa-weight me-1"></i>
                                    {{ report.file_size_mb }} MB
                                </small>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_archived_report', report_id=report.id) }}" 
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteReport({{ report.id }}, '{{ report.filename }}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    تم الإنشاء: {{ report.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {% if loop.last %}
                    </div> <!-- إغلاق المجموعة الأخيرة -->
                {% endif %}
                {% endfor %}
            {% else %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تقارير في الأرشيف</h5>
                        <p class="text-muted">ابدأ بإنشاء تقارير من <a href="{{ url_for('print_news') }}">صفحة الطباعة</a></p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- تقسيم الصفحات -->
        {% if reports.pages > 1 %}
        <nav aria-label="تقسيم الصفحات">
            <ul class="pagination justify-content-center">
                {% if reports.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reports_archive', page=reports.prev_num, report_type=report_type, category_id=category_id, date_from=date_from, date_to=date_to) }}">السابق</a>
                </li>
                {% endif %}
                
                {% for page_num in reports.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != reports.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('reports_archive', page=page_num, report_type=report_type, category_id=category_id, date_from=date_from, date_to=date_to) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if reports.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('reports_archive', page=reports.next_num, report_type=report_type, category_id=category_id, date_from=date_from, date_to=date_to) }}">التالي</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف التقرير "<span id="reportName"></span>" من الأرشيف؟</p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteReport(reportId, reportName) {
            document.getElementById('reportName').textContent = reportName;
            document.getElementById('deleteForm').action = `/reports/archive/${reportId}/delete`;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // تحسين تجربة المستخدم للفلترة
        document.addEventListener('DOMContentLoaded', function() {
            const yearSelect = document.getElementById('year');
            const monthSelect = document.getElementById('month');
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');

            function toggleDateInputs() {
                const hasMonthYear = yearSelect.value && monthSelect.value;
                dateFromInput.disabled = hasMonthYear;
                dateToInput.disabled = hasMonthYear;

                if (hasMonthYear) {
                    dateFromInput.value = '';
                    dateToInput.value = '';
                }
            }

            yearSelect.addEventListener('change', toggleDateInputs);
            monthSelect.addEventListener('change', toggleDateInputs);

            // تطبيق القاعدة عند تحميل الصفحة
            toggleDateInputs();
        });
    </script>
</body>
</html>
