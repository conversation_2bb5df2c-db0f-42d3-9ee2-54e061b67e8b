from firebase_config import FirestoreModel, db, COLLECTIONS
from datetime import datetime, date
from google.cloud.firestore import Query
import json

class Category(FirestoreModel):
    """نموذج التصنيفات"""
    collection_name = COLLECTIONS['categories']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = kwargs.get('name', '')
        self.description = kwargs.get('description', '')
        self.created_at = kwargs.get('created_at', datetime.now())
    
    @classmethod
    def get_all_ordered(cls):
        """جلب جميع التصنيفات مرتبة بالاسم"""
        return cls.get_all(order_by='name', desc=False)
    
    def __str__(self):
        return self.name

class Source(FirestoreModel):
    """نموذج المصادر"""
    collection_name = COLLECTIONS['sources']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = kwargs.get('name', '')
        self.url = kwargs.get('url', '')
        self.created_at = kwargs.get('created_at', datetime.now())
    
    @classmethod
    def get_all_ordered(cls):
        """جلب جميع المصادر مرتبة بالاسم"""
        return cls.get_all(order_by='name', desc=False)
    
    def __str__(self):
        return self.name

class News(FirestoreModel):
    """نموذج الأخبار"""
    collection_name = COLLECTIONS['news']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = kwargs.get('title', '')
        self.content = kwargs.get('content', '')
        self.source = kwargs.get('source', '')
        self.category_id = kwargs.get('category_id', '')
        self.date = kwargs.get('date')
        self.created_at = kwargs.get('created_at', datetime.now())
        
        # تحويل التاريخ إلى date object إذا كان string
        if isinstance(self.date, str):
            try:
                self.date = datetime.strptime(self.date, '%Y-%m-%d').date()
            except:
                self.date = date.today()
        elif isinstance(self.date, datetime):
            self.date = self.date.date()
    
    @classmethod
    def get_by_date_range(cls, start_date, end_date, category_id=None):
        """جلب الأخبار حسب نطاق التاريخ"""
        try:
            query = db.collection(cls.collection_name)
            
            # فلترة حسب التاريخ
            if start_date:
                query = query.where('date', '>=', start_date)
            if end_date:
                query = query.where('date', '<=', end_date)
            
            # فلترة حسب التصنيف
            if category_id:
                query = query.where('category_id', '==', category_id)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            query = query.order_by('date', direction=Query.DESCENDING)
            query = query.order_by('created_at', direction=Query.DESCENDING)
            
            docs = query.stream()
            results = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(cls(**data))
            
            return results
        except Exception as e:
            print(f"خطأ في جلب الأخبار حسب التاريخ: {str(e)}")
            return []
    
    @classmethod
    def get_latest(cls, limit=10):
        """جلب آخر الأخبار"""
        return cls.get_all(limit=limit, order_by='created_at', desc=True)
    
    @classmethod
    def get_by_category(cls, category_id, limit=None):
        """جلب الأخبار حسب التصنيف"""
        return cls.filter_by('category_id', category_id, limit=limit)
    
    @classmethod
    def search(cls, keyword, limit=50):
        """البحث في الأخبار"""
        try:
            # البحث في العنوان والمحتوى
            results = []
            
            # جلب جميع الأخبار (يمكن تحسينها لاحقاً)
            all_news = cls.get_all(limit=limit*2)
            
            keyword_lower = keyword.lower()
            for news in all_news:
                if (keyword_lower in news.title.lower() or 
                    keyword_lower in news.content.lower()):
                    results.append(news)
                    if len(results) >= limit:
                        break
            
            return results
        except Exception as e:
            print(f"خطأ في البحث: {str(e)}")
            return []
    
    def get_category(self):
        """الحصول على التصنيف"""
        if self.category_id:
            return Category.get_by_id(self.category_id)
        return None
    
    def to_dict(self):
        """تحويل إلى dictionary للحفظ"""
        # تحويل date إلى datetime للتوافق مع Firestore
        date_as_datetime = datetime.combine(self.date, datetime.min.time()) if isinstance(self.date, date) else self.date

        data = {
            'title': self.title,
            'content': self.content,
            'source': self.source,
            'category_id': self.category_id,
            'date': date_as_datetime,
            'created_at': self.created_at
        }
        if hasattr(self, 'id') and self.id:
            data['id'] = self.id
        return data

class ReportArchive(FirestoreModel):
    """نموذج أرشيف التقارير"""
    collection_name = COLLECTIONS['report_archive']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.filename = kwargs.get('filename', '')
        self.report_type = kwargs.get('report_type', '')
        self.start_date = kwargs.get('start_date')
        self.end_date = kwargs.get('end_date')
        self.category_id = kwargs.get('category_id', '')
        self.total_news = kwargs.get('total_news', 0)
        self.file_size = kwargs.get('file_size', 0)
        self.html_content = kwargs.get('html_content', '')
        self.created_at = kwargs.get('created_at', datetime.now())
        
        # تحويل التواريخ
        if isinstance(self.start_date, str):
            try:
                self.start_date = datetime.strptime(self.start_date, '%Y-%m-%d').date()
            except:
                pass
        elif isinstance(self.start_date, datetime):
            self.start_date = self.start_date.date()
            
        if isinstance(self.end_date, str):
            try:
                self.end_date = datetime.strptime(self.end_date, '%Y-%m-%d').date()
            except:
                pass
        elif isinstance(self.end_date, datetime):
            self.end_date = self.end_date.date()
    
    @classmethod
    def get_paginated(cls, page=1, per_page=20, filters=None):
        """جلب التقارير مع تقسيم الصفحات"""
        try:
            query = db.collection(cls.collection_name)

            # تطبيق الفلاتر
            if filters:
                if filters.get('report_type'):
                    query = query.where('report_type', '==', filters['report_type'])
                if filters.get('category_id'):
                    query = query.where('category_id', '==', filters['category_id'])
                # ملاحظة: فلترة السنة والشهر معقدة في Firestore، سنتركها للآن

            # ترتيب حسب تاريخ الإنشاء (أبسط من end_date)
            query = query.order_by('created_at', direction=Query.DESCENDING)

            # تقسيم الصفحات
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)

            docs = query.stream()
            results = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(cls(**data))

            return results
        except Exception as e:
            print(f"خطأ في جلب التقارير المقسمة: {str(e)}")
            return []
    
    @classmethod
    def count_all(cls):
        """عدد جميع التقارير"""
        try:
            docs = db.collection(cls.collection_name).stream()
            return len(list(docs))
        except Exception as e:
            print(f"خطأ في عد التقارير: {str(e)}")
            return 0
    
    @classmethod
    def get_monthly_stats(cls, limit=12):
        """إحصائيات شهرية للتقارير"""
        try:
            # جلب جميع التقارير (يمكن تحسينها)
            all_reports = cls.get_all()
            
            # تجميع حسب الشهر
            monthly_data = {}
            for report in all_reports:
                if report.end_date:
                    month_key = f"{report.end_date.year}-{report.end_date.month:02d}"
                    if month_key not in monthly_data:
                        monthly_data[month_key] = {
                            'year': report.end_date.year,
                            'month': report.end_date.month,
                            'count': 0
                        }
                    monthly_data[month_key]['count'] += 1
            
            # ترتيب وتحديد العدد
            sorted_data = sorted(monthly_data.values(), 
                               key=lambda x: (x['year'], x['month']), 
                               reverse=True)
            
            return sorted_data[:limit]
        except Exception as e:
            print(f"خطأ في الإحصائيات الشهرية: {str(e)}")
            return []
    
    @property
    def report_period(self):
        """فترة التقرير بالعربية"""
        if self.start_date and self.end_date:
            if self.start_date == self.end_date:
                return self.end_date.strftime('%Y-%m-%d')
            else:
                start_formatted = self.start_date.strftime('%Y-%m-%d')
                end_formatted = self.end_date.strftime('%Y-%m-%d')
                return f"من {start_formatted} إلى {end_formatted}"
        elif self.end_date:
            return self.end_date.strftime('%Y-%m-%d')
        return "غير محدد"
    
    @property
    def file_size_mb(self):
        """حجم الملف بالميجابايت"""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0
    
    def to_dict(self):
        """تحويل إلى dictionary للحفظ"""
        # تحويل التواريخ إلى datetime للتوافق مع Firestore
        start_date_dt = datetime.combine(self.start_date, datetime.min.time()) if isinstance(self.start_date, date) else self.start_date
        end_date_dt = datetime.combine(self.end_date, datetime.min.time()) if isinstance(self.end_date, date) else self.end_date

        data = {
            'filename': self.filename,
            'report_type': self.report_type,
            'start_date': start_date_dt,
            'end_date': end_date_dt,
            'category_id': self.category_id,
            'total_news': self.total_news,
            'file_size': self.file_size,
            'html_content': self.html_content,
            'created_at': self.created_at
        }
        if hasattr(self, 'id') and self.id:
            data['id'] = self.id
        return data
