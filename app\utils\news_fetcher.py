"""
وحدة جلب الأخبار من المصادر الإخبارية
"""

import requests
from bs4 import BeautifulSoup
import hashlib
import logging
import re
from datetime import datetime, date
from urllib.parse import urljoin, urlparse
import time
import random

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('news_fetcher')

class NewsFetcher:
    """فئة لجلب الأخبار من المصادر الإخبارية"""

    def __init__(self, db, ScrapedNews, NewsSource):
        """تهيئة جالب الأخبار"""
        self.db = db
        self.ScrapedNews = ScrapedNews
        self.NewsSource = NewsSource
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }

    def fetch_all_sources(self, only_active=True, only_iraqi=True, max_news_per_source=30):
        """جلب الأخبار من جميع المصادر النشطة"""
        # جلب المصادر النشطة
        query = self.NewsSource.query
        if only_active:
            query = query.filter_by(is_active=True)
        if only_iraqi:
            query = query.filter_by(is_iraqi=True)

        sources = query.all()
        logger.info(f"بدء جلب الأخبار من {len(sources)} مصدر")

        # إذا لم تكن هناك مصادر، أضف مصدرًا افتراضيًا للاختبار
        if len(sources) == 0:
            default_sources = [
                {
                    'name': 'السومرية نيوز',
                    'url': 'https://www.alsumaria.tv/',
                    'is_iraqi': True
                },
                {
                    'name': 'شبكة أخبار العراق',
                    'url': 'https://aliraqnews.com/',
                    'is_iraqi': True
                },
                {
                    'name': 'بغداد اليوم',
                    'url': 'https://baghdadtoday.news/',
                    'is_iraqi': True
                }
            ]

            for source_data in default_sources:
                source = self.NewsSource(
                    name=source_data['name'],
                    url=source_data['url'],
                    is_active=True,
                    is_iraqi=source_data['is_iraqi']
                )
                self.db.session.add(source)

            self.db.session.commit()
            sources = self.NewsSource.query.filter_by(is_active=True).all()
            logger.info(f"تم إضافة {len(default_sources)} مصدر افتراضي")

        total_fetched = 0
        results = []

        for source in sources:
            try:
                news_list = self.fetch_from_source(source, max_news=max_news_per_source)
                total_fetched += len(news_list)
                results.append({
                    'source': source.name,
                    'count': len(news_list),
                    'status': 'success'
                })
                logger.info(f"تم جلب {len(news_list)} خبر من {source.name}")

                # إضافة فترة انتظار عشوائية بين الطلبات لتجنب الحظر
                time.sleep(random.uniform(1, 3))

            except Exception as e:
                logger.error(f"خطأ في جلب الأخبار من {source.name}: {str(e)}")
                results.append({
                    'source': source.name,
                    'count': 0,
                    'status': 'error',
                    'error': str(e)
                })

        logger.info(f"اكتمل جلب الأخبار. تم جلب {total_fetched} خبر بنجاح")
        return results

    def fetch_from_source(self, source, max_news=30, max_pages=3):
        """جلب الأخبار من مصدر محدد"""
        logger.info(f"جلب الأخبار من {source.name} ({source.url})")

        try:
            news_list = []
            all_news_links = []

            # جلب الصفحة الرئيسية
            main_page_links = self._get_links_from_page(source.url, source)
            all_news_links.extend(main_page_links)

            # جلب روابط من صفحات إضافية (مثل صفحات الأقسام أو الصفحات التالية)
            additional_pages = self._get_additional_pages(source.url, source, max_pages)
            for page_url in additional_pages:
                page_links = self._get_links_from_page(page_url, source)
                all_news_links.extend(page_links)

                # إضافة فترة انتظار عشوائية بين الطلبات لتجنب الحظر
                time.sleep(random.uniform(0.5, 1))

            # إزالة الروابط المكررة
            all_news_links = list(set(all_news_links))
            logger.info(f"تم العثور على {len(all_news_links)} رابط أخبار في {source.name}")

            # جلب محتوى كل خبر
            for i, link in enumerate(all_news_links[:max_news]):
                try:
                    # التحقق من أن الرابط لم يتم جلبه مسبقًا
                    existing_url = self.ScrapedNews.query.filter_by(source_url=link).first()
                    if existing_url:
                        logger.info(f"الخبر موجود مسبقًا (الرابط): {link}")
                        continue

                    news = self._fetch_news_content(link, source)
                    if news:
                        # التحقق من عدم وجود الخبر مسبقًا (بناءً على بصمة المحتوى)
                        existing_news = self.ScrapedNews.query.filter_by(content_hash=news['content_hash']).first()
                        if not existing_news:
                            # إنشاء خبر جديد
                            scraped_news = self.ScrapedNews(
                                title=news['title'],
                                content=news['content'],
                                date=news['date'],
                                source=source.name,
                                source_url=news['url'],
                                content_hash=news['content_hash'],
                                source_id=source.id
                            )
                            self.db.session.add(scraped_news)
                            news_list.append(news)
                            logger.info(f"تم جلب الخبر: {news['title']}")

                            # حفظ التغييرات في قاعدة البيانات بعد كل 5 أخبار
                            if len(news_list) % 5 == 0:
                                self.db.session.commit()
                        else:
                            logger.info(f"الخبر موجود مسبقًا (المحتوى): {news['title']}")

                    # إضافة فترة انتظار عشوائية بين الطلبات لتجنب الحظر
                    time.sleep(random.uniform(0.3, 1))

                except Exception as e:
                    logger.error(f"خطأ في جلب محتوى الخبر {link}: {str(e)}")
                    # محاولة إعادة ضبط الجلسة في حالة حدوث خطأ
                    try:
                        self.db.session.rollback()
                    except:
                        pass

            # حفظ التغييرات المتبقية في قاعدة البيانات
            try:
                self.db.session.commit()
            except Exception as e:
                logger.error(f"خطأ في حفظ التغييرات في قاعدة البيانات: {str(e)}")
                self.db.session.rollback()

            return news_list

        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار من {source.name}: {str(e)}")
            # محاولة إعادة ضبط الجلسة في حالة حدوث خطأ
            try:
                self.db.session.rollback()
            except:
                pass
            return []

    def _get_links_from_page(self, page_url, source):
        """جلب روابط الأخبار من صفحة محددة"""
        try:
            # جلب صفحة المصدر
            response = requests.get(page_url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # التأكد من أن الصفحة بالعربية
            if 'content-type' in response.headers and 'charset' in response.headers['content-type']:
                encoding = response.headers['content-type'].split('charset=')[-1]
                response.encoding = encoding
            else:
                response.encoding = 'utf-8'

            # تحليل الصفحة
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن روابط الأخبار
            news_links = self._extract_news_links(soup, page_url)

            return news_links
        except Exception as e:
            logger.error(f"خطأ في جلب روابط الأخبار من {page_url}: {str(e)}")
            return []

    def _get_additional_pages(self, base_url, source, max_pages=3):
        """الحصول على صفحات إضافية للجلب منها (مثل صفحات الأقسام أو الصفحات التالية)"""
        additional_pages = []

        try:
            # جلب صفحة المصدر
            response = requests.get(base_url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # التأكد من أن الصفحة بالعربية
            if 'content-type' in response.headers and 'charset' in response.headers['content-type']:
                encoding = response.headers['content-type'].split('charset=')[-1]
                response.encoding = encoding
            else:
                response.encoding = 'utf-8'

            # تحليل الصفحة
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن روابط الأقسام
            section_links = []

            # البحث عن روابط في القائمة الرئيسية
            for nav in soup.find_all(['nav', 'ul', 'div'], class_=lambda c: c and any(keyword in c.lower() for keyword in ['menu', 'nav', 'header', 'قائمة', 'رئيسية'])):
                for a in nav.find_all('a', href=True):
                    href = a.get('href')
                    if href and not href.startswith('#') and not href.startswith('javascript:'):
                        # تحويل الرابط النسبي إلى رابط مطلق
                        full_url = urljoin(base_url, href)

                        # التأكد من أن الرابط في نفس النطاق
                        if urlparse(full_url).netloc == urlparse(base_url).netloc:
                            # التحقق من أن الرابط يشير إلى قسم أخبار
                            if any(keyword in href.lower() or keyword in a.text.lower() for keyword in ['news', 'article', 'category', 'section', 'أخبار', 'مقالات', 'أقسام', 'تصنيف', 'عاجل', 'محلية', 'دولية', 'سياسية', 'اقتصادية']):
                                section_links.append(full_url)

            # إضافة الصفحات التالية (2, 3, ...) إذا كانت موجودة
            pagination_links = []
            for a in soup.find_all('a', href=True):
                href = a.get('href')
                if href and not href.startswith('#') and not href.startswith('javascript:'):
                    # البحث عن روابط الصفحات التالية
                    if re.search(r'page=\d+|/page/\d+|[?&]p=\d+', href):
                        # تحويل الرابط النسبي إلى رابط مطلق
                        full_url = urljoin(base_url, href)

                        # التأكد من أن الرابط في نفس النطاق
                        if urlparse(full_url).netloc == urlparse(base_url).netloc:
                            pagination_links.append(full_url)

            # إضافة روابط الأقسام (بحد أقصى 5 أقسام)
            additional_pages.extend(section_links[:5])

            # إضافة روابط الصفحات التالية (بحد أقصى max_pages صفحة)
            additional_pages.extend(pagination_links[:max_pages])

            # إزالة الروابط المكررة
            additional_pages = list(set(additional_pages))

            logger.info(f"تم العثور على {len(additional_pages)} صفحة إضافية في {source.name}")

            return additional_pages
        except Exception as e:
            logger.error(f"خطأ في الحصول على صفحات إضافية من {base_url}: {str(e)}")
            return []

    def _extract_news_links(self, soup, base_url):
        """استخراج روابط الأخبار من صفحة المصدر"""
        links = []

        # البحث عن جميع الروابط في الصفحة
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']

            # تجاهل الروابط الخارجية والروابط غير المتعلقة بالأخبار
            if self._is_news_link(href, a_tag):
                # تحويل الرابط النسبي إلى رابط مطلق
                full_url = urljoin(base_url, href)

                # التأكد من أن الرابط في نفس النطاق
                if urlparse(full_url).netloc == urlparse(base_url).netloc:
                    links.append(full_url)

        # إزالة الروابط المكررة
        return list(set(links))

    def _is_news_link(self, href, a_tag):
        """التحقق مما إذا كان الرابط هو رابط خبر"""
        # تجاهل الروابط الفارغة أو روابط الصفحة الحالية
        if not href or href == '#' or href.startswith('javascript:'):
            return False

        # تجاهل روابط وسائل التواصل الاجتماعي والبريد الإلكتروني
        if href.startswith(('mailto:', 'tel:', 'sms:', 'whatsapp:')):
            return False
        if any(domain in href for domain in ['facebook.com', 'twitter.com', 'instagram.com', 'youtube.com']):
            return False

        # البحث عن كلمات دالة على أنه رابط خبر
        news_keywords = ['news', 'article', 'story', 'post', 'خبر', 'مقال', 'قصة', 'منشور', 'تقرير', 'report']

        # كلمات دالة على أخبار العراق
        iraq_keywords = ['iraq', 'iraqi', 'العراق', 'عراقي', 'عراقية', 'بغداد', 'baghdad', 'موصل', 'mosul', 'basra', 'البصرة', 'كركوك', 'kirkuk', 'اربيل', 'erbil']

        # التحقق من وجود كلمات دالة في الرابط
        if any(keyword in href.lower() for keyword in news_keywords):
            # التحقق من وجود كلمات دالة على العراق في الرابط أو نص الرابط
            if any(keyword in href.lower() for keyword in iraq_keywords):
                return True
            if a_tag.text and any(keyword in a_tag.text.lower() for keyword in iraq_keywords):
                return True

        # التحقق من وجود كلمات دالة في نص الرابط
        if a_tag.text and any(keyword in a_tag.text.lower() for keyword in news_keywords):
            # التحقق من وجود كلمات دالة على العراق في الرابط أو نص الرابط
            if any(keyword in href.lower() for keyword in iraq_keywords):
                return True
            if a_tag.text and any(keyword in a_tag.text.lower() for keyword in iraq_keywords):
                return True

        # التحقق من وجود تاريخ في الرابط (مثل 2023/05/18)
        if re.search(r'\d{4}/\d{1,2}/\d{1,2}', href):
            # التحقق من وجود كلمات دالة على العراق في الرابط أو نص الرابط
            if any(keyword in href.lower() for keyword in iraq_keywords):
                return True
            if a_tag.text and any(keyword in a_tag.text.lower() for keyword in iraq_keywords):
                return True

        # التحقق من وجود معرف رقمي في الرابط (مثل id=123 أو news/123)
        if re.search(r'id=\d+', href) or re.search(r'/\d+$', href):
            # التحقق من وجود كلمات دالة على العراق في الرابط أو نص الرابط
            if any(keyword in href.lower() for keyword in iraq_keywords):
                return True
            if a_tag.text and any(keyword in a_tag.text.lower() for keyword in iraq_keywords):
                return True

        return False

    def _fetch_news_content(self, url, source):
        """جلب محتوى الخبر من الرابط"""
        try:
            # جلب صفحة الخبر
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            # التأكد من أن الصفحة بالعربية
            if 'content-type' in response.headers and 'charset' in response.headers['content-type']:
                encoding = response.headers['content-type'].split('charset=')[-1]
                response.encoding = encoding
            else:
                response.encoding = 'utf-8'

            # تحليل الصفحة
            soup = BeautifulSoup(response.text, 'html.parser')

            # استخراج عنوان الخبر
            title = self._extract_title(soup)
            if not title:
                logger.warning(f"لم يتم العثور على عنوان للخبر: {url}")
                return None

            # استخراج محتوى الخبر
            content = self._extract_content(soup)
            if not content:
                logger.warning(f"لم يتم العثور على محتوى للخبر: {url}")
                return None

            # استخراج تاريخ الخبر
            news_date = self._extract_date(soup)

            # التحقق من أن الخبر يتعلق بالعراق
            if not self._is_iraq_related(title, content):
                logger.info(f"الخبر لا يتعلق بالعراق: {title}")
                return None

            # التحقق من أن الخبر منشور في تاريخ اليوم
            if not self._is_today(news_date):
                logger.info(f"الخبر ليس من تاريخ اليوم: {title} - {news_date}")
                return None

            # إنشاء بصمة المحتوى
            hash_text = f"{title}|{content}"
            content_hash = hashlib.md5(hash_text.encode('utf-8')).hexdigest()

            return {
                'title': title,
                'content': content,
                'date': news_date,
                'url': url,
                'content_hash': content_hash
            }

        except Exception as e:
            logger.error(f"خطأ في جلب محتوى الخبر {url}: {str(e)}")
            return None

    def _is_iraq_related(self, title, content):
        """التحقق من أن الخبر يتعلق بالعراق"""
        # كلمات دالة على أخبار العراق
        iraq_keywords = ['iraq', 'iraqi', 'العراق', 'عراقي', 'عراقية', 'بغداد', 'baghdad', 'موصل', 'mosul', 'basra', 'البصرة', 'كركوك', 'kirkuk', 'اربيل', 'erbil',
                        'النجف', 'najaf', 'كربلاء', 'karbala', 'الانبار', 'anbar', 'ديالى', 'diyala', 'ذي قار', 'dhi qar', 'ميسان', 'maysan', 'المثنى', 'muthanna',
                        'صلاح الدين', 'salah al-din', 'بابل', 'babylon', 'واسط', 'wasit', 'دهوك', 'duhok', 'السليمانية', 'sulaymaniyah']

        # التحقق من وجود كلمات دالة على العراق في العنوان
        if any(keyword in title.lower() for keyword in iraq_keywords):
            return True

        # التحقق من وجود كلمات دالة على العراق في المحتوى
        if any(keyword in content.lower() for keyword in iraq_keywords):
            return True

        return False

    def _is_today(self, news_date):
        """التحقق من أن الخبر منشور في تاريخ اليوم"""
        from datetime import date, timedelta

        # إذا كان التاريخ غير محدد، نفترض أنه من تاريخ اليوم
        if not news_date:
            return True

        today = date.today()

        # نقبل الأخبار المنشورة في تاريخ اليوم أو الأمس (لمراعاة فروق التوقيت)
        return news_date >= today - timedelta(days=1)

    def _extract_title(self, soup):
        """استخراج عنوان الخبر"""
        # محاولة العثور على العنوان في وسم h1
        h1 = soup.find('h1')
        if h1:
            return h1.text.strip()

        # محاولة العثور على العنوان في وسم title
        title = soup.find('title')
        if title:
            # إزالة اسم الموقع من العنوان إذا وجد
            title_text = title.text.strip()
            site_name = soup.find('meta', property='og:site_name')
            if site_name and site_name.get('content'):
                title_text = title_text.replace(f" - {site_name.get('content')}", "")
                title_text = title_text.replace(f" | {site_name.get('content')}", "")

            return title_text

        # محاولة العثور على العنوان في وسم meta
        meta_title = soup.find('meta', property='og:title')
        if meta_title and meta_title.get('content'):
            return meta_title.get('content').strip()

        return None

    def _extract_content(self, soup):
        """استخراج محتوى الخبر"""
        # محاولة العثور على المحتوى في وسوم المقالات الشائعة
        article = soup.find('article')
        if article:
            # إزالة العناصر غير المرغوب فيها
            for element in article.find_all(['script', 'style', 'iframe', 'aside', 'nav']):
                element.decompose()

            # الحصول على النص
            paragraphs = article.find_all('p')
            if paragraphs:
                return '\n\n'.join([p.text.strip() for p in paragraphs if p.text.strip()])

            return article.text.strip()

        # محاولة العثور على المحتوى في وسوم div مع فئات محددة
        content_divs = soup.find_all('div', class_=lambda c: c and any(keyword in c.lower() for keyword in ['content', 'article', 'story', 'text', 'body', 'entry', 'post', 'محتوى', 'مقال', 'نص']))
        if content_divs:
            for div in content_divs:
                # إزالة العناصر غير المرغوب فيها
                for element in div.find_all(['script', 'style', 'iframe', 'aside', 'nav']):
                    element.decompose()

                # الحصول على النص
                paragraphs = div.find_all('p')
                if paragraphs:
                    content = '\n\n'.join([p.text.strip() for p in paragraphs if p.text.strip()])
                    if len(content) > 100:  # التأكد من أن المحتوى كافٍ
                        return content

        # محاولة العثور على المحتوى في وسوم p
        paragraphs = soup.find_all('p')
        if paragraphs:
            content = '\n\n'.join([p.text.strip() for p in paragraphs if p.text.strip()])
            if len(content) > 100:  # التأكد من أن المحتوى كافٍ
                return content

        return None

    def _extract_date(self, soup):
        """استخراج تاريخ الخبر"""
        # محاولة العثور على التاريخ في وسوم الوقت
        time_tag = soup.find('time')
        if time_tag and time_tag.get('datetime'):
            try:
                return datetime.fromisoformat(time_tag.get('datetime').split('T')[0]).date()
            except:
                pass

        # محاولة العثور على التاريخ في وسوم meta
        meta_date_properties = ['article:published_time', 'og:published_time', 'published_time', 'date', 'pubdate']
        for prop in meta_date_properties:
            meta_date = soup.find('meta', property=prop) or soup.find('meta', attrs={'name': prop})
            if meta_date and meta_date.get('content'):
                try:
                    date_str = meta_date.get('content')
                    if 'T' in date_str:
                        return datetime.fromisoformat(date_str.split('T')[0]).date()
                    elif ' ' in date_str:
                        return datetime.fromisoformat(date_str.split(' ')[0]).date()
                    else:
                        return datetime.fromisoformat(date_str).date()
                except:
                    pass

        # محاولة العثور على التاريخ في عناصر HTML مع سمات تاريخ
        date_attrs = ['pubdate', 'datetime', 'date-time', 'date']
        for attr in date_attrs:
            for element in soup.find_all(attrs={attr: True}):
                try:
                    date_str = element[attr]
                    if 'T' in date_str:
                        return datetime.fromisoformat(date_str.split('T')[0]).date()
                    elif ' ' in date_str:
                        return datetime.fromisoformat(date_str.split(' ')[0]).date()
                    else:
                        return datetime.fromisoformat(date_str).date()
                except:
                    pass

        # محاولة العثور على التاريخ في النص
        date_patterns = [
            # أنماط التاريخ العربية
            r'(\d{1,2})[/.-](\d{1,2})[/.-](\d{4})',  # DD/MM/YYYY
            r'(\d{4})[/.-](\d{1,2})[/.-](\d{1,2})',  # YYYY/MM/DD
            r'(\d{1,2}) (يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر) (\d{4})',  # DD Month YYYY
            r'(\d{1,2}) (كانون الثاني|شباط|آذار|نيسان|أيار|حزيران|تموز|آب|أيلول|تشرين الأول|تشرين الثاني|كانون الأول) (\d{4})',  # DD Month YYYY (Levant)
            # أنماط التاريخ الإنجليزية
            r'(\d{1,2}) (January|February|March|April|May|June|July|August|September|October|November|December) (\d{4})',  # DD Month YYYY
            r'(January|February|March|April|May|June|July|August|September|October|November|December) (\d{1,2}), (\d{4})',  # Month DD, YYYY
            r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{1,2}), (\d{4})',  # Mon DD, YYYY
            r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\.? (\d{1,2}), (\d{4})',  # Mon. DD, YYYY
            # أنماط التاريخ الشائعة في المواقع العربية
            r'(\d{1,2})-([\u0621-\u064A]+)-(\d{4})',  # DD-MonthArabic-YYYY
            r'(\d{1,2}) ([\u0621-\u064A]+) (\d{4})',  # DD MonthArabic YYYY
            r'([\u0621-\u064A]+) (\d{1,2}), (\d{4})',  # MonthArabic DD, YYYY
            # أنماط التاريخ الهجري
            r'(\d{1,2}) (محرم|صفر|ربيع الأول|ربيع الثاني|جمادى الأولى|جمادى الآخرة|رجب|شعبان|رمضان|شوال|ذو القعدة|ذو الحجة) (\d{4})',  # DD Month YYYY (Hijri)
        ]

        # البحث عن عناصر HTML التي قد تحتوي على تاريخ
        date_classes = ['date', 'time', 'datetime', 'published', 'pubdate', 'timestamp', 'article-date', 'post-date', 'entry-date', 'meta-date', 'date-published']
        date_elements = []

        # البحث عن عناصر بفئات محددة
        for cls in date_classes:
            date_elements.extend(soup.find_all(class_=lambda c: c and cls in c.lower()))

        # البحث عن عناصر بسمات محددة
        date_elements.extend(soup.find_all(['time', 'span', 'div', 'p']))

        # البحث عن التاريخ في النص
        for element in date_elements:
            text = element.text.strip()
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    try:
                        if 'يناير' in pattern or 'كانون' in pattern or 'January' in pattern or 'Jan' in pattern:
                            # تحويل اسم الشهر إلى رقم
                            arabic_months = {
                                'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4, 'مايو': 5, 'يونيو': 6,
                                'يوليو': 7, 'أغسطس': 8, 'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12,
                                'كانون الثاني': 1, 'شباط': 2, 'آذار': 3, 'نيسان': 4, 'أيار': 5, 'حزيران': 6,
                                'تموز': 7, 'آب': 8, 'أيلول': 9, 'تشرين الأول': 10, 'تشرين الثاني': 11, 'كانون الأول': 12,
                                'January': 1, 'February': 2, 'March': 3, 'April': 4, 'May': 5, 'June': 6,
                                'July': 7, 'August': 8, 'September': 9, 'October': 10, 'November': 11, 'December': 12,
                                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
                                # أسماء الأشهر العربية المختصرة
                                'ينا': 1, 'فبر': 2, 'مار': 3, 'أبر': 4, 'ماي': 5, 'يون': 6,
                                'يول': 7, 'أغس': 8, 'سبت': 9, 'أكت': 10, 'نوف': 11, 'ديس': 12,
                                # أسماء الأشهر الهجرية
                                'محرم': 1, 'صفر': 2, 'ربيع الأول': 3, 'ربيع الثاني': 4, 'جمادى الأولى': 5, 'جمادى الآخرة': 6,
                                'رجب': 7, 'شعبان': 8, 'رمضان': 9, 'شوال': 10, 'ذو القعدة': 11, 'ذو الحجة': 12
                            }

                            if 'Month DD, YYYY' in pattern or 'Mon DD, YYYY' in pattern:
                                month = arabic_months[match.group(1)]
                                day = int(match.group(2))
                                year = int(match.group(3))
                            else:
                                day = int(match.group(1))
                                month = arabic_months[match.group(2)]
                                year = int(match.group(3))

                            # التحقق من صحة التاريخ
                            if 1 <= day <= 31 and 1 <= month <= 12 and 2000 <= year <= 2100:
                                return date(year, month, day)
                        else:
                            # تحويل التاريخ الرقمي
                            if pattern.startswith(r'(\d{4})'):  # YYYY/MM/DD
                                year = int(match.group(1))
                                month = int(match.group(2))
                                day = int(match.group(3))
                            else:  # DD/MM/YYYY
                                day = int(match.group(1))
                                month = int(match.group(2))
                                year = int(match.group(3))

                            # التحقق من صحة التاريخ
                            if 1 <= day <= 31 and 1 <= month <= 12 and 2000 <= year <= 2100:
                                return date(year, month, day)
                    except:
                        pass

        # إذا لم يتم العثور على تاريخ، استخدم تاريخ اليوم
        return date.today()
