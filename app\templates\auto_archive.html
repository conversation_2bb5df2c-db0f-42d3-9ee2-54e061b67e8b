<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأرشفة التلقائية - نظام الرصد الإعلامي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .archive-stats {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .date-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .date-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .archived {
            border-left: 4px solid #28a745;
            background-color: #f8fff9;
        }
        .pending {
            border-left: 4px solid #ffc107;
            background-color: #fffdf5;
        }
        .archive-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }
        .archive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            color: white;
        }
        .progress-section {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='img/logo2.png') }}" alt="شعار النظام" height="40">
                نظام الرصد الإعلامي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_news') }}">الأخبار</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('print_news') }}">طباعة التقارير</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports_archive') }}">أرشيف التقارير</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('auto_archive_page') }}">الأرشفة التلقائية</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- إحصائيات الأرشفة -->
        <div class="archive-stats">
            <h2 class="text-center mb-4">
                <i class="fas fa-robot me-2"></i>الأرشفة التلقائية للأخبار
            </h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ total_dates }}</h3>
                        <p><i class="fas fa-calendar-day me-2"></i>إجمالي الأيام</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ archived_dates_count }}</h3>
                        <p><i class="fas fa-check-circle me-2"></i>مؤرشف</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card">
                        <h3>{{ pending_dates }}</h3>
                        <p><i class="fas fa-clock me-2"></i>في الانتظار</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التقدم -->
        {% if total_dates > 0 %}
        <div class="progress-section">
            <h5><i class="fas fa-chart-line me-2"></i>تقدم الأرشفة</h5>
            <div class="progress mb-3" style="height: 25px;">
                {% set progress_percentage = (archived_dates_count / total_dates * 100)|round(1) %}
                <div class="progress-bar bg-success" role="progressbar" 
                     style="width: {{ progress_percentage }}%" 
                     aria-valuenow="{{ progress_percentage }}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                    {{ progress_percentage }}%
                </div>
            </div>
            <p class="text-muted mb-0">
                تم أرشفة {{ archived_dates_count }} من أصل {{ total_dates }} يوم 
                ({{ progress_percentage }}%)
            </p>
        </div>
        {% endif %}

        <!-- زر الأرشفة التلقائية -->
        {% if pending_dates > 0 %}
        <div class="text-center mb-4">
            <form method="POST" action="{{ url_for('auto_archive_news') }}" id="archiveForm">
                <button type="button" class="archive-button" onclick="startArchiving()">
                    <i class="fas fa-magic me-2"></i>
                    بدء الأرشفة التلقائية لـ {{ pending_dates }} يوم
                </button>
            </form>
        </div>
        {% else %}
        <div class="text-center mb-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                تم أرشفة جميع الأيام بنجاح!
            </div>
        </div>
        {% endif %}

        <!-- قائمة الأيام -->
        <div class="row">
            <div class="col-12">
                <h4><i class="fas fa-list me-2"></i>حالة الأرشفة لكل يوم</h4>
            </div>
        </div>

        <div class="row">
            {% for item in archive_status %}
            <div class="col-md-6 col-lg-4">
                <div class="date-card {% if item.is_archived %}archived{% else %}pending{% endif %}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ item.date_formatted }}</h6>
                            <small class="text-muted">
                                <i class="fas fa-newspaper me-1"></i>
                                {{ item.news_count }} خبر
                            </small>
                        </div>
                        <div>
                            {% if item.is_archived %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>مؤرشف
                                </span>
                            {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>في الانتظار
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- نافذة تأكيد الأرشفة -->
    <div class="modal fade" id="archiveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الأرشفة التلقائية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من بدء الأرشفة التلقائية؟</p>
                    <p>سيتم إنشاء تقرير يومي لكل تاريخ يحتوي على أخبار وحفظه في الأرشيف.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> هذه العملية قد تستغرق بعض الوقت حسب عدد الأيام المطلوب أرشفتها.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitArchive()">
                        <i class="fas fa-magic me-2"></i>بدء الأرشفة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التحميل -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h5>جاري الأرشفة التلقائية...</h5>
                    <p class="text-muted">يرجى الانتظار، هذه العملية قد تستغرق بعض الوقت.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startArchiving() {
            new bootstrap.Modal(document.getElementById('archiveModal')).show();
        }

        function submitArchive() {
            // إخفاء نافذة التأكيد
            bootstrap.Modal.getInstance(document.getElementById('archiveModal')).hide();
            
            // إظهار نافذة التحميل
            new bootstrap.Modal(document.getElementById('loadingModal')).show();
            
            // إرسال النموذج
            document.getElementById('archiveForm').submit();
        }
    </script>
</body>
</html>
