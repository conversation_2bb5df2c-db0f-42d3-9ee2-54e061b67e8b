"""
وحدة الزحف واستخراج الأخبار من المواقع الإلكترونية
"""
import requests
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime, date
import logging
import time
import random
from urllib.parse import urlparse, urljoin

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('news_scraper')

# قائمة الكلمات المفتاحية للعراق
IRAQ_KEYWORDS = [
    # المحافظات والمدن
    'العراق', 'عراقي', 'عراقية', 'بغداد', 'البصرة', 'الموصل', 'نينوى', 'أربيل', 'النجف',
    'كربلاء', 'ذي قار', 'الأنبار', 'ديالى', 'واسط', 'ميسان', 'المثنى', 'صلاح الدين',
    'بابل', 'القادسية', 'كركوك', 'دهوك', 'السليمانية', 'الناصرية', 'الحلة', 'الديوانية',
    'الرمادي', 'الفلوجة', 'سامراء', 'تكريت', 'الكوت', 'العمارة', 'الكوفة', 'الحي', 'الزبير',
    'الفاو', 'خانقين', 'بعقوبة', 'الحويجة', 'طوزخورماتو', 'الشرقاط', 'هيت', 'حديثة', 'عنه',
    'راوة', 'القائم', 'الرطبة', 'الخالدية', 'الحبانية', 'الصقلاوية', 'المحمودية', 'اليوسفية',
    'المدائن', 'الطارمية', 'أبو غريب', 'الكاظمية', 'الأعظمية', 'الكرخ', 'الرصافة',

    # المؤسسات والهيئات
    'الحكومة العراقية', 'البرلمان العراقي', 'مجلس النواب', 'مجلس الوزراء', 'رئاسة الجمهورية',
    'وزارة', 'الوزراء', 'المفوضية', 'الهيئة', 'المحكمة الاتحادية', 'القضاء العراقي',
    'الجيش العراقي', 'الشرطة العراقية', 'الحشد الشعبي', 'البيشمركة', 'القوات الأمنية',
    'البنك المركزي العراقي', 'سوق العراق للأوراق المالية', 'شركة النفط الوطنية',

    # الشخصيات
    'رئيس الجمهورية', 'رئيس الوزراء', 'رئيس البرلمان', 'وزير', 'محافظ', 'نائب', 'عضو البرلمان',

    # قضايا عراقية
    'النفط العراقي', 'الدينار العراقي', 'الانتخابات العراقية', 'الموازنة العراقية',
    'الاقتصاد العراقي', 'التظاهرات', 'الاحتجاجات', 'الإرهاب', 'داعش', 'الأمن',
    'إقليم كردستان', 'الحكومة الاتحادية', 'المنطقة الخضراء', 'المنطقة الدولية',

    # كلمات عامة مرتبطة بالعراق
    'العراقيين', 'العراقيون', 'بلاد الرافدين', 'ما بين النهرين', 'دجلة', 'الفرات'
]

class NewsScraper:
    """فئة للزحف واستخراج الأخبار من المواقع الإلكترونية"""

    def __init__(self, source, user_agent=None):
        """
        تهيئة الزاحف

        :param source: كائن مصدر الأخبار (NewsSource)
        :param user_agent: عنوان المستخدم للطلبات HTTP
        """
        self.source = source

        # قائمة عناوين المستخدم المختلفة للتناوب بينها
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
        ]

        self.user_agent = user_agent or self.user_agents[0]
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        })

    def fetch_page(self, url, max_retries=2, timeout=10):
        """
        جلب صفحة ويب

        :param url: عنوان URL للصفحة
        :param max_retries: عدد المحاولات القصوى
        :param timeout: مهلة الاتصال بالثواني
        :return: كائن BeautifulSoup
        """
        for retry in range(max_retries):
            try:
                # تغيير عنوان المستخدم في كل محاولة
                if retry > 0:
                    import random
                    new_user_agent = random.choice(self.user_agents)
                    self.session.headers.update({'User-Agent': new_user_agent})
                    logger.info(f"تغيير عنوان المستخدم في المحاولة {retry+1}")
                    # إضافة تأخير قصير قبل إعادة المحاولة
                    time.sleep(random.uniform(0.5, 1.5))

                # إضافة معلمات عشوائية لتجنب التخزين المؤقت
                params = {'_': str(int(time.time() * 1000))}

                # تعيين مهلة أقصر للاتصال
                response = self.session.get(url, timeout=timeout, params=params)

                # التحقق من الاستجابة
                if response.status_code == 403:
                    logger.warning(f"تم رفض الوصول (403) لـ {url}")
                    continue

                response.raise_for_status()

                # التحقق من محتوى الصفحة بشكل سريع
                if len(response.content) < 300:
                    logger.warning(f"محتوى الصفحة {url} قصير جداً")
                    if retry < max_retries - 1:
                        continue

                # استخدام محلل HTML أسرع
                return BeautifulSoup(response.content, 'lxml')

            except requests.exceptions.Timeout:
                logger.warning(f"انتهت مهلة الاتصال بـ {url}")
                if retry < max_retries - 1:
                    continue

            except requests.exceptions.HTTPError as e:
                logger.error(f"خطأ HTTP في جلب الصفحة {url}: {str(e)}")
                if retry < max_retries - 1:
                    continue

            except Exception as e:
                logger.error(f"خطأ في جلب الصفحة {url}: {str(e)}")
                if retry < max_retries - 1:
                    continue

        logger.error(f"فشلت محاولات جلب الصفحة {url}")
        return None

    def extract_links(self, soup, base_url):
        """
        استخراج روابط الأخبار من صفحة الويب

        :param soup: كائن BeautifulSoup
        :param base_url: عنوان URL الأساسي
        :return: قائمة بروابط الأخبار
        """
        links = []
        for a in soup.find_all('a', href=True):
            href = a['href']
            # تحويل الروابط النسبية إلى روابط مطلقة
            full_url = urljoin(base_url, href)
            # التحقق من أن الرابط ينتمي لنفس الموقع
            if urlparse(full_url).netloc == urlparse(base_url).netloc:
                links.append(full_url)

        return list(set(links))  # إزالة الروابط المكررة

    def is_news_page(self, url, soup):
        """
        التحقق مما إذا كانت الصفحة هي صفحة خبر

        :param url: عنوان URL للصفحة
        :param soup: كائن BeautifulSoup
        :return: True إذا كانت صفحة خبر، False خلاف ذلك
        """
        # التحقق من وجود عنوان الخبر
        if self.source.selector_title:
            title_element = soup.select_one(self.source.selector_title)
            if not title_element:
                return False

        # التحقق من وجود محتوى الخبر
        if self.source.selector_content:
            content_element = soup.select_one(self.source.selector_content)
            if not content_element:
                return False

        # يمكن إضافة المزيد من الشروط هنا

        return True

    def extract_news(self, url):
        """
        استخراج بيانات الخبر من صفحة الويب

        :param url: عنوان URL لصفحة الخبر
        :return: قاموس يحتوي على بيانات الخبر أو None في حالة الفشل
        """
        soup = self.fetch_page(url)
        if not soup:
            return None

        if not self.is_news_page(url, soup):
            return None

        # استخراج عنوان الخبر
        title = None
        if self.source.selector_title:
            title_element = soup.select_one(self.source.selector_title)
            if title_element:
                title = title_element.get_text().strip()

        # استخراج محتوى الخبر
        content = None
        if self.source.selector_content:
            content_element = soup.select_one(self.source.selector_content)
            if content_element:
                content = content_element.get_text().strip()

        # استخراج تاريخ الخبر
        news_date = None
        if self.source.selector_date:
            date_element = soup.select_one(self.source.selector_date)
            if date_element:
                date_text = date_element.get_text().strip()
                news_date = self.parse_date(date_text)

        # إذا لم يتم العثور على تاريخ، استخدم تاريخ اليوم
        if not news_date:
            news_date = date.today()

        # التحقق من أن الخبر يتعلق بالعراق
        if not self.is_related_to_iraq(title, content):
            logger.debug(f"تم تجاهل الخبر لأنه غير متعلق بالعراق: {title}")
            return None

        # التحقق من تاريخ النشر
        today = date.today()
        max_days_old = 3  # السماح بأخبار من آخر 3 أيام

        if (today - news_date).days > max_days_old:
            logger.debug(f"تم تجاهل الخبر لأنه قديم ({news_date}): {title}")
            return None

        # إضافة معلومات إضافية للخبر
        news_freshness = "اليوم"
        if news_date == today:
            news_freshness = "اليوم"
        elif news_date == today - timedelta(days=1):
            news_freshness = "أمس"
        else:
            news_freshness = f"منذ {(today - news_date).days} أيام"

        return {
            'title': title,
            'content': content,
            'date': news_date,
            'source': self.source.name,
            'source_url': url,
            'freshness': news_freshness,
            'iraq_related': True
        }

    def parse_date(self, date_text):
        """
        تحليل نص التاريخ واستخراج كائن تاريخ

        :param date_text: نص التاريخ
        :return: كائن date أو None في حالة الفشل
        """
        if not date_text:
            return None

        # تنظيف النص
        date_text = date_text.strip()

        # إزالة الكلمات الشائعة التي قد تظهر مع التاريخ
        common_words = ['نشر', 'تاريخ', 'بتاريخ', 'في', 'النشر', 'تم', 'الساعة', 'يوم', 'تحديث']
        for word in common_words:
            date_text = date_text.replace(word, ' ')

        date_text = ' '.join(date_text.split())  # إزالة المسافات الزائدة

        try:
            # إذا كان هناك صيغة تاريخ محددة
            if self.source.date_format and self.source.date_format != 'arabic_month':
                try:
                    return datetime.strptime(date_text, self.source.date_format).date()
                except ValueError:
                    pass

            # البحث عن تاريخ اليوم في النص
            today_patterns = ['اليوم', 'منذ ساعات', 'منذ دقائق', 'قبل قليل', 'الآن', 'حالاً', 'مؤخراً', 'للتو']
            for pattern in today_patterns:
                if pattern in date_text:
                    logger.info(f"تم اكتشاف تاريخ اليوم من النمط: {pattern}")
                    return date.today()

            # البحث عن تاريخ الأمس في النص
            yesterday_patterns = ['أمس', 'البارحة', 'منذ يوم']
            for pattern in yesterday_patterns:
                if pattern in date_text:
                    logger.info(f"تم اكتشاف تاريخ الأمس من النمط: {pattern}")
                    return date.today() - timedelta(days=1)

            # البحث عن أنماط التاريخ الرقمية الشائعة
            # نمط: 25-08-2022, 19:38 أو 25-08-2022 19:38
            numeric_date_pattern1 = r'(\d{1,2})[-/.](\d{1,2})[-/.](\d{4})(?:[,،]\s*|\s+)(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?'
            match = re.search(numeric_date_pattern1, date_text)
            if match:
                day = int(match.group(1))
                month = int(match.group(2))
                year = int(match.group(3))
                try:
                    return date(year, month, day)
                except ValueError:
                    # قد يكون الشهر واليوم معكوسين
                    try:
                        return date(year, day, month)
                    except ValueError:
                        pass

            # نمط: 2022-08-25 19:38:00 أو 2022/08/25 19:38
            numeric_date_pattern2 = r'(\d{4})[-/.](\d{1,2})[-/.](\d{1,2})(?:[,،]\s*|\s+)(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?'
            match = re.search(numeric_date_pattern2, date_text)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                day = int(match.group(3))
                try:
                    return date(year, month, day)
                except ValueError:
                    pass

            # نمط: 25-08-2022 أو 25/08/2022 أو 25.08.2022
            numeric_date_pattern3 = r'(\d{1,2})[-/.](\d{1,2})[-/.](\d{4})'
            match = re.search(numeric_date_pattern3, date_text)
            if match:
                day = int(match.group(1))
                month = int(match.group(2))
                year = int(match.group(3))
                try:
                    return date(year, month, day)
                except ValueError:
                    # قد يكون الشهر واليوم معكوسين
                    try:
                        return date(year, day, month)
                    except ValueError:
                        pass

            # نمط: 2022-08-25 أو 2022/08/25 أو 2022.08.25
            numeric_date_pattern4 = r'(\d{4})[-/.](\d{1,2})[-/.](\d{1,2})'
            match = re.search(numeric_date_pattern4, date_text)
            if match:
                year = int(match.group(1))
                month = int(match.group(2))
                day = int(match.group(3))
                try:
                    return date(year, month, day)
                except ValueError:
                    pass

            # محاولة تحليل التاريخ بصيغ مختلفة
            date_formats = [
                # صيغ التاريخ الأساسية
                '%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%Y/%m/%d',
                '%Y.%m.%d', '%d.%m.%Y', '%m/%d/%Y', '%d %m %Y',

                # صيغ التاريخ مع الوقت
                '%Y-%m-%dT%H:%M:%S', '%Y-%m-%d %H:%M:%S',
                '%d/%m/%Y %H:%M:%S', '%d-%m-%Y %H:%M:%S',
                '%Y/%m/%d %H:%M:%S', '%Y.%m.%d %H:%M:%S',

                # صيغ التاريخ مع الوقت بصيغة 12 ساعة
                '%Y-%m-%d %I:%M:%S %p', '%d/%m/%Y %I:%M:%S %p',
                '%d-%m-%Y %I:%M %p', '%Y/%m/%d %I:%M %p',

                # صيغ التاريخ مع الوقت بدون ثواني
                '%Y-%m-%d %H:%M', '%d/%m/%Y %H:%M',
                '%d-%m-%Y %H:%M', '%Y/%m/%d %H:%M',

                # صيغ التاريخ مع فواصل منقوطة
                '%d-%m-%Y، %H:%M', '%d/%m/%Y، %H:%M',
                '%d-%m-%Y، %I:%M %p', '%d/%m/%Y، %I:%M %p',

                # صيغ التاريخ مع فواصل عادية
                '%d-%m-%Y, %H:%M', '%d/%m/%Y, %H:%M',
                '%d-%m-%Y, %I:%M %p', '%d/%m/%Y, %I:%M %p'
            ]

            for fmt in date_formats:
                try:
                    return datetime.strptime(date_text, fmt).date()
                except ValueError:
                    continue

            # البحث عن أنماط التاريخ في النص
            # مثال: 17 مايو 2024
            arabic_months = {
                # أسماء الأشهر الميلادية العربية (مصر والخليج)
                'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4, 'مايو': 5, 'يونيو': 6,
                'يوليو': 7, 'أغسطس': 8, 'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12,

                # أسماء الأشهر الميلادية العربية (العراق وسوريا ولبنان وفلسطين والأردن)
                'كانون الثاني': 1, 'شباط': 2, 'آذار': 3, 'نيسان': 4, 'أيار': 5, 'حزيران': 6,
                'تموز': 7, 'آب': 8, 'أيلول': 9, 'تشرين الأول': 10, 'تشرين الثاني': 11, 'كانون الأول': 12,

                # أسماء الأشهر الميلادية العربية (المغرب العربي)
                'جانفي': 1, 'فيفري': 2, 'مارس': 3, 'أفريل': 4, 'ماي': 5, 'جوان': 6,
                'جويلية': 7, 'أوت': 8, 'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12,

                # أسماء الأشهر مع تنسيقات مختلفة
                'ينايـر': 1, 'فبرايـر': 2, 'مـارس': 3, 'أبريـل': 4, 'مايـو': 5, 'يونيـو': 6,
                'يوليـو': 7, 'أغسطـس': 8, 'سبتمبـر': 9, 'أكتوبـر': 10, 'نوفمبـر': 11, 'ديسمبـر': 12,

                # أسماء الأشهر المختصرة
                'يناير': 1, 'فبراير': 2, 'مارس': 3, 'ابريل': 4, 'مايو': 5, 'يونيو': 6,
                'يوليو': 7, 'اغسطس': 8, 'سبتمبر': 9, 'اكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12,

                # أسماء الأشهر بدون همزات
                'يناير': 1, 'فبراير': 2, 'مارس': 3, 'ابريل': 4, 'مايو': 5, 'يونيو': 6,
                'يوليو': 7, 'اغسطس': 8, 'سبتمبر': 9, 'اكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12,

                # أسماء الأشهر بالإنجليزية (للمواقع التي تستخدم أسماء إنجليزية في النص العربي)
                'january': 1, 'february': 2, 'march': 3, 'april': 4, 'may': 5, 'june': 6,
                'july': 7, 'august': 8, 'september': 9, 'october': 10, 'november': 11, 'december': 12,
                'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
            }

            # البحث عن نمط: اليوم - الشهر - السنة
            for month_name, month_num in arabic_months.items():
                # نمط: 17 مايو 2024
                pattern1 = r'(\d{1,2})\s+' + month_name + r'\s+(\d{4})'
                match = re.search(pattern1, date_text, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    year = int(match.group(2))
                    return date(year, month_num, day)

                # نمط: مايو 17، 2024
                pattern2 = month_name + r'\s+(\d{1,2})[،,]\s+(\d{4})'
                match = re.search(pattern2, date_text, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    year = int(match.group(2))
                    return date(year, month_num, day)

                # نمط: 17/مايو/2024 أو 17-مايو-2024
                pattern3 = r'(\d{1,2})[-/]\s*' + month_name + r'\s*[-/](\d{4})'
                match = re.search(pattern3, date_text, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    year = int(match.group(2))
                    return date(year, month_num, day)

                # نمط: الخميس 17 مايو 2024
                pattern4 = r'(?:الأحد|الإثنين|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)\s+(\d{1,2})\s+' + month_name + r'\s+(\d{4})'
                match = re.search(pattern4, date_text, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    year = int(match.group(2))
                    return date(year, month_num, day)

                # نمط: 17 مايو
                pattern5 = r'(\d{1,2})\s+' + month_name
                match = re.search(pattern5, date_text, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    year = date.today().year
                    return date(year, month_num, day)

                # نمط: مايو 2024
                pattern6 = month_name + r'\s+(\d{4})'
                match = re.search(pattern6, date_text, re.IGNORECASE)
                if match:
                    year = int(match.group(1))
                    # استخدام اليوم الأول من الشهر
                    return date(year, month_num, 1)

            # البحث عن نمط: منذ X يوم/أيام/ساعة/دقيقة
            days_ago_pattern = r'منذ\s+(\d+)\s+(يوم|أيام|ساعة|ساعات|دقيقة|دقائق|ثانية|ثوان)'
            match = re.search(days_ago_pattern, date_text)
            if match:
                num = int(match.group(1))
                unit = match.group(2)
                if unit in ['يوم', 'أيام']:
                    return date.today() - timedelta(days=num)
                elif unit in ['ساعة', 'ساعات', 'دقيقة', 'دقائق', 'ثانية', 'ثوان']:
                    return date.today()

            # البحث عن نمط: قبل X يوم/أيام/ساعة/دقيقة
            days_ago_pattern2 = r'قبل\s+(\d+)\s+(يوم|أيام|ساعة|ساعات|دقيقة|دقائق|ثانية|ثوان)'
            match = re.search(days_ago_pattern2, date_text)
            if match:
                num = int(match.group(1))
                unit = match.group(2)
                if unit in ['يوم', 'أيام']:
                    return date.today() - timedelta(days=num)
                elif unit in ['ساعة', 'ساعات', 'دقيقة', 'دقائق', 'ثانية', 'ثوان']:
                    return date.today()

            # البحث عن نمط: منذ ساعتين/ساعة/دقيقتين/دقيقة
            time_ago_pattern = r'منذ\s+(ساعتين|ساعة|دقيقتين|دقيقة|ثانيتين|ثانية)'
            match = re.search(time_ago_pattern, date_text)
            if match:
                return date.today()

            # البحث عن نمط: قبل ساعتين/ساعة/دقيقتين/دقيقة
            time_ago_pattern2 = r'قبل\s+(ساعتين|ساعة|دقيقتين|دقيقة|ثانيتين|ثانية)'
            match = re.search(time_ago_pattern2, date_text)
            if match:
                return date.today()

            # البحث عن تاريخ بصيغة هجرية وتحويله
            hijri_months = {
                'محرم': 1, 'صفر': 2, 'ربيع الأول': 3, 'ربيع الثاني': 4, 'ربيع الآخر': 4,
                'جمادى الأولى': 5, 'جمادى الأول': 5, 'جمادى الآخرة': 6, 'جمادى الآخر': 6, 'جمادى الثاني': 6,
                'رجب': 7, 'شعبان': 8, 'رمضان': 9, 'شوال': 10, 'ذو القعدة': 11, 'ذي القعدة': 11,
                'ذو الحجة': 12, 'ذي الحجة': 12
            }

            # نمط: 15 رمضان 1445 هـ
            hijri_pattern1 = r'(\d{1,2})\s+(' + '|'.join(hijri_months.keys()) + r')\s+(\d{4})\s+(?:هـ|هجري|هجرية)'
            match = re.search(hijri_pattern1, date_text)
            if match:
                logger.info(f"تم اكتشاف تاريخ هجري: {match.group(0)}")
                # يمكن استخدام مكتبة لتحويل التاريخ الهجري إلى ميلادي في المستقبل
                # حاليًا نستخدم تاريخ اليوم كتقريب
                return date.today()

            # نمط: 15/رمضان/1445 هـ
            hijri_pattern2 = r'(\d{1,2})[-/]\s*(' + '|'.join(hijri_months.keys()) + r')\s*[-/](\d{4})\s+(?:هـ|هجري|هجرية)'
            match = re.search(hijri_pattern2, date_text)
            if match:
                logger.info(f"تم اكتشاف تاريخ هجري: {match.group(0)}")
                return date.today()

            # نمط: الخميس 15 رمضان 1445 هـ
            hijri_pattern3 = r'(?:الأحد|الإثنين|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)\s+(\d{1,2})\s+(' + '|'.join(hijri_months.keys()) + r')\s+(\d{4})\s+(?:هـ|هجري|هجرية)'
            match = re.search(hijri_pattern3, date_text)
            if match:
                logger.info(f"تم اكتشاف تاريخ هجري: {match.group(0)}")
                return date.today()

            # إذا لم يتم العثور على تاريخ، استخدم تاريخ اليوم
            logger.warning(f"لم يتم التعرف على صيغة التاريخ: {date_text}")
            return date.today()

        except Exception as e:
            logger.error(f"خطأ في تحليل التاريخ '{date_text}': {str(e)}")
            return date.today()

    def is_related_to_iraq(self, title, content):
        """
        التحقق مما إذا كان الخبر يتعلق بالعراق - نسخة مبسطة وسريعة

        :param title: عنوان الخبر
        :param content: محتوى الخبر
        :return: True إذا كان الخبر يتعلق بالعراق، False خلاف ذلك
        """
        if not title and not content:
            return False

        # التحقق السريع من العنوان أولاً (أكثر فعالية)
        if title:
            # قائمة مختصرة من الكلمات المفتاحية الرئيسية للعراق
            main_keywords = ['العراق', 'عراقي', 'عراقية', 'بغداد', 'الموصل', 'البصرة', 'كربلاء', 'النجف', 'الأنبار', 'كركوك']
            for keyword in main_keywords:
                if keyword in title:
                    logger.info(f"الخبر متعلق بالعراق (كلمة مفتاحية في العنوان): {keyword}")
                    return True

        # إذا لم يتم العثور على كلمات مفتاحية في العنوان، نتحقق من المحتوى
        if content:
            # دمج العنوان والمحتوى للبحث
            text = f"{title} {content}" if title else content

            # قائمة مختصرة من الكلمات المفتاحية للعراق
            iraq_keywords_short = [
                'العراق', 'عراقي', 'عراقية', 'بغداد', 'الموصل', 'البصرة', 'كربلاء', 'النجف', 'الأنبار', 'كركوك',
                'ديالى', 'صلاح الدين', 'نينوى', 'ذي قار', 'ميسان', 'واسط', 'المثنى', 'القادسية', 'بابل',
                'الحكومة العراقية', 'البرلمان العراقي', 'مجلس النواب', 'رئيس الوزراء العراقي'
            ]

            for keyword in iraq_keywords_short:
                if keyword in text:
                    logger.info(f"الخبر متعلق بالعراق (كلمة مفتاحية في المحتوى): {keyword}")
                    return True

            # إذا كان المصدر عراقي، نفترض أن الخبر متعلق بالعراق
            if hasattr(self.source, 'is_iraqi') and self.source.is_iraqi:
                logger.info(f"الخبر متعلق بالعراق (المصدر عراقي): {self.source.name}")
                return True

        logger.debug(f"الخبر غير متعلق بالعراق: {title}")
        return False

    def scrape(self, max_pages=2, max_news=5):
        """
        الزحف واستخراج الأخبار من المصدر

        :param max_pages: الحد الأقصى لعدد الصفحات للزحف
        :param max_news: الحد الأقصى لعدد الأخبار للاستخراج
        :return: قائمة بالأخبار المستخرجة
        """
        news_list = []
        visited_urls = set()
        urls_to_visit = [self.source.url]

        # إضافة أقسام الأخبار المحلية والعراقية إذا كانت متوفرة
        common_sections = [
            '/iraq', '/local', '/news/iraq', '/news/local', '/category/iraq', '/category/local',
            '/ar/iraq', '/ar/local', '/ar/news/iraq', '/ar/news/local',
            '/العراق', '/محلي', '/محليات', '/أخبار-العراق', '/أخبار-محلية'
        ]

        # إضافة فقط أقسام الأخبار الأكثر صلة
        for section in common_sections[:5]:  # استخدام أول 5 أقسام فقط
            section_url = urljoin(self.source.url, section)
            if section_url not in urls_to_visit and section_url != self.source.url:
                urls_to_visit.append(section_url)

        # عدد المحاولات الفاشلة المتتالية
        consecutive_failures = 0
        max_consecutive_failures = 3  # تقليل عدد المحاولات

        # تحديد وقت بدء الزحف
        start_time = time.time()
        max_crawl_time = 60  # الحد الأقصى للزحف هو 60 ثانية

        while urls_to_visit and len(visited_urls) < max_pages and len(news_list) < max_news:
            # التحقق من الوقت المنقضي
            if time.time() - start_time > max_crawl_time:
                logger.warning(f"تم تجاوز الحد الأقصى لوقت الزحف ({max_crawl_time} ثانية)")
                break

            # أخذ عنوان URL من القائمة
            current_url = urls_to_visit.pop(0)

            # تجنب زيارة نفس العنوان مرتين
            if current_url in visited_urls:
                continue

            logger.info(f"جاري زيارة: {current_url}")
            visited_urls.add(current_url)

            # جلب الصفحة
            soup = self.fetch_page(current_url)
            if not soup:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logger.warning(f"تم تجاوز الحد الأقصى للمحاولات الفاشلة المتتالية ({max_consecutive_failures})")
                    break
                continue

            # إعادة تعيين عداد المحاولات الفاشلة
            consecutive_failures = 0

            # استخراج الأخبار إذا كانت صفحة خبر
            if self.is_news_page(current_url, soup):
                news = self.extract_news(current_url)
                if news:
                    # التحقق من عدم وجود خبر مشابه
                    is_duplicate = False
                    for existing_news in news_list:
                        if existing_news['title'] == news['title']:
                            is_duplicate = True
                            break

                    if not is_duplicate:
                        news_list.append(news)
                        logger.info(f"تم استخراج الخبر: {news['title']}")

            # استخراج روابط جديدة فقط إذا لم نصل إلى الحد الأقصى من الأخبار
            if len(news_list) < max_news:
                # استخراج روابط جديدة (فقط الروابط التي تبدو كروابط أخبار)
                new_links = []
                for a in soup.find_all('a', href=True):
                    href = a['href']
                    # تحويل الروابط النسبية إلى روابط مطلقة
                    full_url = urljoin(self.source.url, href)
                    # التحقق من أن الرابط ينتمي لنفس الموقع
                    if urlparse(full_url).netloc == urlparse(self.source.url).netloc:
                        # التحقق من أن الرابط يبدو كرابط خبر
                        if any(keyword in href.lower() for keyword in ['news', 'article', 'post', 'story', 'خبر', 'مقال']):
                            new_links.append(full_url)

                # إضافة الروابط الجديدة (بحد أقصى 10 روابط)
                for link in new_links[:10]:
                    if link not in visited_urls and link not in urls_to_visit:
                        urls_to_visit.append(link)

            # تأخير عشوائي قصير لتجنب الحظر
            time.sleep(random.uniform(0.5, 1.0))

        logger.info(f"تم استخراج {len(news_list)} خبر من {self.source.name}")
        return news_list
