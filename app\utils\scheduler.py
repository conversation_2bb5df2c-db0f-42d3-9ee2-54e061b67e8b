"""
وحدة جدولة المهام لجلب الأخبار تلقائيًا
"""
import logging
import asyncio
import json
from datetime import datetime, timedelta
import hashlib

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from flask import current_app

from app.models.relational_models import db, NewsSource, News, FetchLog
from app.utils.news_scraper import NewsScraper
from app.utils.headless_browser import scrape_with_headless_browser

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('scheduler')

class NewsScheduler:
    """فئة جدولة مهام جلب الأخبار"""

    def __init__(self, app=None):
        """
        تهيئة المجدول

        :param app: تطبيق Flask
        """
        self.app = app
        self.scheduler = BackgroundScheduler()

        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        """
        تهيئة المجدول مع تطبيق Flask

        :param app: تطبيق Flask
        """
        self.app = app

        # إضافة مهمة جلب الأخبار
        self.scheduler.add_job(
            func=self.fetch_news_job,
            trigger=CronTrigger(hour='*/3'),  # كل 3 ساعات
            id='fetch_news_job',
            name='جلب الأخبار تلقائيًا',
            replace_existing=True
        )

        # إضافة مهمة تنظيف قاعدة البيانات
        self.scheduler.add_job(
            func=self.cleanup_job,
            trigger=CronTrigger(hour=2, minute=0),  # الساعة 2 صباحًا
            id='cleanup_job',
            name='تنظيف قاعدة البيانات',
            replace_existing=True
        )

        # في وضع التطوير، لا نقوم بتشغيل المجدول تلقائيًا
        # يمكن للمستخدم تشغيله يدويًا من واجهة المستخدم
        if not app.debug:
            self.start()

            # إيقاف المجدول عند إيقاف التطبيق
            @app.teardown_appcontext
            def shutdown_scheduler(exception=None):
                self.shutdown()

    def start(self):
        """بدء تشغيل المجدول"""
        if not self.scheduler.running:
            self.scheduler.start()
            logger.info("تم بدء تشغيل المجدول")

    def shutdown(self):
        """إيقاف المجدول"""
        if self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("تم إيقاف المجدول")

    async def fetch_news_from_source_async(self, source):
        """جلب الأخبار من مصدر واحد بشكل غير متزامن"""
        try:
            news_list = []

            # استخدام المتصفح بدون واجهة للمواقع المعقدة فقط
            if source.source_type == 'website' and source.complexity == 'high':
                # استخدام المتصفح بدون واجهة
                news_list = await scrape_with_headless_browser(source, max_pages=2, max_news=5)
                method = 'headless_browser'
            else:
                # استخدام الزاحف العادي
                scraper = NewsScraper(source)
                news_list = scraper.scrape(max_pages=2, max_news=5)
                method = 'regular_scraper'

            # تحديث وقت آخر جلب
            source.last_fetch = datetime.now()

            # إضافة سجل جلب جديد
            log = FetchLog(
                source_id=source.id,
                status='success',
                news_count=len(news_list)
            )

            return source, news_list, log

        except Exception as e:
            # إضافة سجل جلب مع خطأ
            log = FetchLog(
                source_id=source.id,
                status='error',
                error_message=str(e)
            )
            logger.error(f"خطأ في جلب الأخبار من {source.name}: {str(e)}")
            return source, [], log

    def fetch_news_job(self):
        """مهمة جلب الأخبار تلقائيًا"""
        print("بدء مهمة جلب الأخبار تلقائيًا")
        logger.info("بدء مهمة جلب الأخبار تلقائيًا")

        # التحقق من وجود سياق التطبيق
        if not self.app:
            print("خطأ: لا يوجد سياق للتطبيق")
            logger.error("خطأ: لا يوجد سياق للتطبيق")
            return

        with self.app.app_context():
            print("تم الدخول إلى سياق التطبيق")

            # جلب المصادر النشطة فقط
            sources = NewsSource.query.filter_by(is_active=True).all()

            # تحديد أولوية المصادر (المصادر التي لم يتم جلبها منذ فترة طويلة لها أولوية أعلى)
            now = datetime.now()
            for source in sources:
                if not source.last_fetch:
                    source.priority = 10  # أعلى أولوية للمصادر التي لم يتم جلبها أبدًا
                else:
                    # حساب الأولوية بناءً على الوقت المنقضي منذ آخر جلب
                    hours_since_last_fetch = (now - source.last_fetch).total_seconds() / 3600
                    source.priority = min(10, int(hours_since_last_fetch / 3))

            # ترتيب المصادر حسب الأولوية
            sources.sort(key=lambda s: s.priority, reverse=True)

            # اختيار أعلى 5 مصادر فقط
            sources = sources[:5]

            total_news = 0
            content_hashes = set()  # مجموعة لتخزين بصمات المحتوى لمنع التكرار

            # جلب الأخبار من المصادر بالتوازي
            async def fetch_all_sources():
                tasks = []
                for source in sources:
                    tasks.append(self.fetch_news_from_source_async(source))
                return await asyncio.gather(*tasks)

            results = asyncio.run(fetch_all_sources())

            for source, news_list, log in results:
                db.session.add(log)

                # إضافة معلومات إضافية للسجل
                try:
                    if news_list:
                        log.config = json.dumps({
                            'method': 'parallel_fetch',
                            'news_count': len(news_list),
                            'news_titles': [news['title'] for news in news_list[:3]]  # تخزين أول 3 عناوين فقط
                        })

                        # حفظ الأخبار في قاعدة البيانات (فقط أخبار اليوم)
                        saved_count = 0
                        today = datetime.now().date()

                        for news_item in news_list:
                            # التحقق من تاريخ النشر (فقط أخبار اليوم)
                            if isinstance(news_item.get('date'), str):
                                try:
                                    date_obj = datetime.strptime(news_item['date'], '%Y-%m-%d').date()
                                except:
                                    date_obj = today
                            else:
                                date_obj = news_item.get('date', today)

                            # تجاهل الأخبار القديمة (أكثر من يوم واحد)
                            if (today - date_obj).days > 1:
                                continue

                            # التحقق من أن الخبر يتعلق بالعراق
                            if not news_item.get('iraq_related', False):
                                continue

                            # إنشاء بصمة المحتوى إذا لم تكن موجودة
                            content_hash = news_item.get('content_hash', '')
                            if not content_hash:
                                hash_text = f"{news_item['title']}|{news_item['content']}"
                                content_hash = hashlib.md5(hash_text.encode('utf-8')).hexdigest()
                                news_item['content_hash'] = content_hash

                            # تجاهل الأخبار المكررة
                            if content_hash in content_hashes:
                                continue

                            # التحقق من عدم وجود خبر مشابه في قاعدة البيانات
                            existing_news = News.query.filter(
                                db.or_(
                                    db.and_(News.title == news_item['title'], News.source_name == source.name),
                                    News.content_hash == content_hash
                                )
                            ).first()

                            if existing_news:
                                continue

                            # إنشاء خبر جديد
                            news = News(
                                title=news_item['title'],
                                content=news_item['content'],
                                date=date_obj,
                                source_name=source.name,
                                source_url=news_item.get('source_url', ''),
                                content_hash=content_hash,
                                # استخدام محافظة وتصنيف افتراضي
                                governorate_id=1,  # بغداد
                                category_id=1  # أخبار عامة
                            )
                            db.session.add(news)
                            saved_count += 1
                            content_hashes.add(content_hash)

                        total_news += saved_count
                        # تحديث سجل الجلب بعدد الأخبار المحفوظة
                        log.news_count = saved_count
                        logger.info(f"تم حفظ {saved_count} خبر من {source.name}")

                except Exception as e:
                    # إضافة سجل جلب مع خطأ
                    log = FetchLog(
                        source_id=source.id,
                        status='error',
                        error_message=str(e)
                    )
                    db.session.add(log)
                    logger.error(f"خطأ في جلب الأخبار من {source.name}: {str(e)}")

            db.session.commit()
            logger.info(f"انتهت مهمة جلب الأخبار تلقائيًا. تم حفظ {total_news} خبر جديد.")

    def cleanup_job(self):
        """مهمة تنظيف قاعدة البيانات"""
        with self.app.app_context():
            logger.info("بدء مهمة تنظيف قاعدة البيانات")

            # حذف سجلات الجلب القديمة (أكثر من 30 يوم)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            old_logs = FetchLog.query.filter(FetchLog.fetch_date < thirty_days_ago).all()

            for log in old_logs:
                db.session.delete(log)

            db.session.commit()
            logger.info(f"تم حذف {len(old_logs)} سجل جلب قديم")


# إنشاء كائن المجدول
scheduler = NewsScheduler()
