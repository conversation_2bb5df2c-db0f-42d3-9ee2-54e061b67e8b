"""
وحدة استخراج البيانات المهيكلة من صفحات الويب
تستخدم للحصول على بيانات منظمة من علامات meta وSchema.org
"""
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import re

from bs4 import BeautifulSoup
import requests

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('structured_data')

class StructuredDataExtractor:
    """فئة استخراج البيانات المهيكلة من صفحات الويب"""
    
    def __init__(self, html_content=None, url=None):
        """
        تهيئة مستخرج البيانات المهيكلة
        
        :param html_content: محتوى HTML للصفحة
        :param url: عنوان URL للصفحة (إذا لم يتم توفير محتوى HTML)
        """
        self.html_content = html_content
        self.url = url
        self.soup = None
        
        if html_content:
            self.soup = BeautifulSoup(html_content, 'lxml')
        elif url:
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                self.html_content = response.text
                self.soup = BeautifulSoup(self.html_content, 'lxml')
            except Exception as e:
                logger.error(f"خطأ في جلب الصفحة {url}: {str(e)}")
    
    def extract_all(self) -> Dict[str, Any]:
        """
        استخراج جميع البيانات المهيكلة من الصفحة
        
        :return: قاموس بجميع البيانات المهيكلة
        """
        if not self.soup:
            return {}
        
        return {
            'schema_org': self.extract_schema_org(),
            'open_graph': self.extract_open_graph(),
            'twitter_cards': self.extract_twitter_cards(),
            'meta_tags': self.extract_meta_tags(),
            'dublin_core': self.extract_dublin_core()
        }
    
    def extract_schema_org(self) -> List[Dict[str, Any]]:
        """
        استخراج بيانات Schema.org من الصفحة
        
        :return: قائمة بقواميس بيانات Schema.org
        """
        if not self.soup:
            return []
        
        schema_data = []
        
        # استخراج بيانات JSON-LD
        json_ld_scripts = self.soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                data = json.loads(script.string)
                schema_data.append(data)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"خطأ في تحليل بيانات JSON-LD: {str(e)}")
        
        # استخراج بيانات Microdata
        items = self.soup.find_all(itemscope=True)
        for item in items:
            try:
                item_type = item.get('itemtype', '')
                if not item_type:
                    continue
                
                item_data = {'@type': item_type.split('/')[-1]}
                
                # استخراج خصائص العنصر
                props = item.find_all(itemprop=True)
                for prop in props:
                    prop_name = prop.get('itemprop', '')
                    if not prop_name:
                        continue
                    
                    # استخراج قيمة الخاصية
                    if prop.name == 'meta':
                        prop_value = prop.get('content', '')
                    elif prop.name == 'link':
                        prop_value = prop.get('href', '')
                    elif prop.name == 'time':
                        prop_value = prop.get('datetime', '')
                    elif prop.name in ['img', 'audio', 'video', 'source']:
                        prop_value = prop.get('src', '')
                    else:
                        prop_value = prop.get_text().strip()
                    
                    item_data[prop_name] = prop_value
                
                schema_data.append(item_data)
            except Exception as e:
                logger.warning(f"خطأ في استخراج بيانات Microdata: {str(e)}")
        
        return schema_data
    
    def extract_open_graph(self) -> Dict[str, str]:
        """
        استخراج بيانات Open Graph من الصفحة
        
        :return: قاموس ببيانات Open Graph
        """
        if not self.soup:
            return {}
        
        og_data = {}
        
        # استخراج وسوم meta لـ Open Graph
        meta_tags = self.soup.find_all('meta', property=re.compile(r'^og:'))
        for tag in meta_tags:
            property_name = tag.get('property', '').replace('og:', '')
            content = tag.get('content', '')
            if property_name and content:
                og_data[property_name] = content
        
        return og_data
    
    def extract_twitter_cards(self) -> Dict[str, str]:
        """
        استخراج بيانات Twitter Cards من الصفحة
        
        :return: قاموس ببيانات Twitter Cards
        """
        if not self.soup:
            return {}
        
        twitter_data = {}
        
        # استخراج وسوم meta لـ Twitter Cards
        meta_tags = self.soup.find_all('meta', attrs={'name': re.compile(r'^twitter:')})
        for tag in meta_tags:
            property_name = tag.get('name', '').replace('twitter:', '')
            content = tag.get('content', '')
            if property_name and content:
                twitter_data[property_name] = content
        
        return twitter_data
    
    def extract_meta_tags(self) -> Dict[str, str]:
        """
        استخراج وسوم meta العامة من الصفحة
        
        :return: قاموس بوسوم meta
        """
        if not self.soup:
            return {}
        
        meta_data = {}
        
        # استخراج وسوم meta العامة
        important_meta_tags = ['description', 'keywords', 'author', 'news_keywords', 'article:published_time', 'article:modified_time', 'article:section', 'article:tag']
        
        for name in important_meta_tags:
            meta_tag = self.soup.find('meta', attrs={'name': name}) or self.soup.find('meta', attrs={'property': name})
            if meta_tag and meta_tag.get('content'):
                meta_data[name] = meta_tag.get('content')
        
        return meta_data
    
    def extract_dublin_core(self) -> Dict[str, str]:
        """
        استخراج بيانات Dublin Core من الصفحة
        
        :return: قاموس ببيانات Dublin Core
        """
        if not self.soup:
            return {}
        
        dc_data = {}
        
        # استخراج وسوم meta لـ Dublin Core
        meta_tags = self.soup.find_all('meta', attrs={'name': re.compile(r'^DC\.')})
        for tag in meta_tags:
            property_name = tag.get('name', '').replace('DC.', '')
            content = tag.get('content', '')
            if property_name and content:
                dc_data[property_name] = content
        
        return dc_data
    
    def get_article_data(self) -> Dict[str, Any]:
        """
        استخراج بيانات المقال من البيانات المهيكلة
        
        :return: قاموس ببيانات المقال
        """
        all_data = self.extract_all()
        article_data = {
            'title': None,
            'description': None,
            'content': None,
            'published_date': None,
            'modified_date': None,
            'author': None,
            'section': None,
            'tags': [],
            'image': None,
            'url': self.url
        }
        
        # استخراج العنوان
        if 'open_graph' in all_data and 'title' in all_data['open_graph']:
            article_data['title'] = all_data['open_graph']['title']
        elif 'twitter_cards' in all_data and 'title' in all_data['twitter_cards']:
            article_data['title'] = all_data['twitter_cards']['title']
        elif 'meta_tags' in all_data and 'title' in all_data['meta_tags']:
            article_data['title'] = all_data['meta_tags']['title']
        elif self.soup and self.soup.title:
            article_data['title'] = self.soup.title.get_text().strip()
        
        # استخراج الوصف
        if 'open_graph' in all_data and 'description' in all_data['open_graph']:
            article_data['description'] = all_data['open_graph']['description']
        elif 'twitter_cards' in all_data and 'description' in all_data['twitter_cards']:
            article_data['description'] = all_data['twitter_cards']['description']
        elif 'meta_tags' in all_data and 'description' in all_data['meta_tags']:
            article_data['description'] = all_data['meta_tags']['description']
        
        # استخراج المحتوى من Schema.org
        if 'schema_org' in all_data and all_data['schema_org']:
            for item in all_data['schema_org']:
                if isinstance(item, dict):
                    if '@type' in item and item['@type'] in ['NewsArticle', 'Article', 'BlogPosting']:
                        if 'articleBody' in item:
                            article_data['content'] = item['articleBody']
                        
                        if 'datePublished' in item:
                            article_data['published_date'] = item['datePublished']
                        
                        if 'dateModified' in item:
                            article_data['modified_date'] = item['dateModified']
                        
                        if 'author' in item:
                            if isinstance(item['author'], dict) and 'name' in item['author']:
                                article_data['author'] = item['author']['name']
                            elif isinstance(item['author'], str):
                                article_data['author'] = item['author']
        
        # استخراج تاريخ النشر من وسوم meta
        if not article_data['published_date'] and 'meta_tags' in all_data:
            if 'article:published_time' in all_data['meta_tags']:
                article_data['published_date'] = all_data['meta_tags']['article:published_time']
        
        # استخراج القسم والوسوم
        if 'meta_tags' in all_data:
            if 'article:section' in all_data['meta_tags']:
                article_data['section'] = all_data['meta_tags']['article:section']
            
            if 'article:tag' in all_data['meta_tags']:
                tags = all_data['meta_tags']['article:tag']
                if isinstance(tags, str):
                    article_data['tags'] = [tag.strip() for tag in tags.split(',')]
            
            if 'keywords' in all_data['meta_tags']:
                keywords = all_data['meta_tags']['keywords']
                if isinstance(keywords, str):
                    article_data['tags'].extend([keyword.strip() for keyword in keywords.split(',')])
        
        # استخراج الصورة
        if 'open_graph' in all_data and 'image' in all_data['open_graph']:
            article_data['image'] = all_data['open_graph']['image']
        elif 'twitter_cards' in all_data and 'image' in all_data['twitter_cards']:
            article_data['image'] = all_data['twitter_cards']['image']
        
        return article_data
