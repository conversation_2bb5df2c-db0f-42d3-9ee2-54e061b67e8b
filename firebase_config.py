import os
import json
import firebase_admin
from firebase_admin import credentials, firestore
from datetime import datetime

class FirebaseConfig:
    """إعداد Firebase للتطبيق"""
    
    def __init__(self):
        self.db = None
        self.app = None
        self.initialize_firebase()
    
    def initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            # التحقق من وجود Firebase app مسبقاً
            if not firebase_admin._apps:
                # إعداد Service Account من متغير البيئة أو ملف
                service_account_key = os.environ.get('FIREBASE_SERVICE_ACCOUNT_KEY')
                
                if service_account_key:
                    # من متغير البيئة (للاستضافة)
                    service_account_info = json.loads(service_account_key)
                    cred = credentials.Certificate(service_account_info)
                else:
                    # من ملف محلي (للتطوير)
                    cred = credentials.Certificate('firebase-service-account.json')
                
                # تهيئة Firebase
                self.app = firebase_admin.initialize_app(cred)
            else:
                self.app = firebase_admin.get_app()
            
            # الحصول على Firestore client
            self.db = firestore.client()
            print("✅ تم الاتصال بـ Firebase بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ Firebase: {str(e)}")
            raise e
    
    def get_db(self):
        """الحصول على Firestore database"""
        return self.db

# إنشاء instance واحد للتطبيق
firebase_config = FirebaseConfig()
db = firebase_config.get_db()

# Firebase Collections Names
COLLECTIONS = {
    'news': 'news',
    'categories': 'categories', 
    'sources': 'sources',
    'report_archive': 'report_archive'
}

class FirestoreModel:
    """Base class للنماذج التي تستخدم Firestore"""
    
    collection_name = None
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @classmethod
    def create(cls, data):
        """إنشاء مستند جديد"""
        try:
            doc_ref = db.collection(cls.collection_name).document()
            data['id'] = doc_ref.id
            data['created_at'] = datetime.now()
            doc_ref.set(data)
            return cls(**data)
        except Exception as e:
            print(f"خطأ في إنشاء {cls.collection_name}: {str(e)}")
            return None
    
    @classmethod
    def get_by_id(cls, doc_id):
        """الحصول على مستند بالمعرف"""
        try:
            doc = db.collection(cls.collection_name).document(doc_id).get()
            if doc.exists:
                data = doc.to_dict()
                data['id'] = doc.id
                return cls(**data)
            return None
        except Exception as e:
            print(f"خطأ في جلب {cls.collection_name}: {str(e)}")
            return None
    
    @classmethod
    def get_all(cls, limit=None, order_by=None, desc=True):
        """الحصول على جميع المستندات"""
        try:
            query = db.collection(cls.collection_name)
            
            if order_by:
                direction = firestore.Query.DESCENDING if desc else firestore.Query.ASCENDING
                query = query.order_by(order_by, direction=direction)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            results = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(cls(**data))
            
            return results
        except Exception as e:
            print(f"خطأ في جلب {cls.collection_name}: {str(e)}")
            return []
    
    @classmethod
    def filter_by(cls, field, value, operator='==', limit=None):
        """فلترة المستندات"""
        try:
            query = db.collection(cls.collection_name).where(field, operator, value)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            results = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(cls(**data))
            
            return results
        except Exception as e:
            print(f"خطأ في فلترة {cls.collection_name}: {str(e)}")
            return []
    
    def save(self):
        """حفظ التغييرات"""
        try:
            data = self.__dict__.copy()
            doc_id = data.pop('id', None)
            data['updated_at'] = datetime.now()
            
            if doc_id:
                db.collection(self.collection_name).document(doc_id).update(data)
            else:
                doc_ref = db.collection(self.collection_name).document()
                data['id'] = doc_ref.id
                data['created_at'] = datetime.now()
                doc_ref.set(data)
                self.id = doc_ref.id
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ {self.collection_name}: {str(e)}")
            return False
    
    def delete(self):
        """حذف المستند"""
        try:
            if self.id:
                db.collection(self.collection_name).document(self.id).delete()
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف {self.collection_name}: {str(e)}")
            return False

def test_connection():
    """اختبار الاتصال بـ Firebase"""
    try:
        # محاولة إنشاء مجموعة اختبار
        test_ref = db.collection('test').document('connection_test')
        test_ref.set({
            'message': 'اختبار الاتصال',
            'timestamp': datetime.now()
        })
        
        # قراءة البيانات
        doc = test_ref.get()
        if doc.exists:
            print("✅ اختبار الاتصال نجح!")
            # حذف بيانات الاختبار
            test_ref.delete()
            return True
        else:
            print("❌ فشل اختبار الاتصال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {str(e)}")
        return False

if __name__ == "__main__":
    # اختبار الاتصال عند تشغيل الملف مباشرة
    test_connection()
