"""
سكريبت تهجير البيانات من SQLite إلى Firebase Firestore
"""

import os
import sys
from datetime import datetime

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def migrate_data():
    """تهجير البيانات من SQLite إلى Firestore"""
    
    print("🚀 بدء تهجير البيانات من SQLite إلى Firebase...")
    
    try:
        # استيراد النماذج القديمة (SQLite)
        from app.models.relational_models import Category as OldCategory, News as OldNews, ReportArchive as OldReportArchive
        from app import app, db as old_db
        
        # استيراد النماذج الجديدة (Firebase)
        from firebase_models import Category, News, ReportArchive
        from firebase_config import test_connection
        
        # اختبار الاتصال بـ Firebase
        if not test_connection():
            print("❌ فشل الاتصال بـ Firebase. تأكد من إعداد Service Account Key.")
            return False
        
        with app.app_context():
            print("\n📊 إحصائيات البيانات الحالية:")
            
            # إحصائيات SQLite
            old_categories = OldCategory.query.all()
            old_news = OldNews.query.all()
            old_reports = OldReportArchive.query.all()
            
            print(f"   - التصنيفات: {len(old_categories)}")
            print(f"   - الأخبار: {len(old_news)}")
            print(f"   - التقارير المؤرشفة: {len(old_reports)}")
            
            # 1. تهجير التصنيفات
            print("\n📁 تهجير التصنيفات...")
            categories_migrated = 0
            category_mapping = {}  # لربط المعرفات القديمة بالجديدة
            
            for old_cat in old_categories:
                try:
                    new_category = Category.create({
                        'name': old_cat.name,
                        'description': getattr(old_cat, 'description', ''),
                        'created_at': getattr(old_cat, 'created_at', datetime.now())
                    })
                    
                    if new_category:
                        category_mapping[old_cat.id] = new_category.id
                        categories_migrated += 1
                        print(f"   ✅ {old_cat.name}")
                    else:
                        print(f"   ❌ فشل في تهجير: {old_cat.name}")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في تهجير التصنيف {old_cat.name}: {str(e)}")
            
            print(f"   📊 تم تهجير {categories_migrated} من {len(old_categories)} تصنيف")
            
            # 2. تهجير الأخبار
            print("\n📰 تهجير الأخبار...")
            news_migrated = 0
            
            for old_news_item in old_news:
                try:
                    # ربط التصنيف الجديد
                    new_category_id = category_mapping.get(old_news_item.category_id, '')
                    
                    new_news = News.create({
                        'title': old_news_item.title,
                        'content': old_news_item.content,
                        'source': old_news_item.source,
                        'category_id': new_category_id,
                        'date': old_news_item.date,
                        'created_at': getattr(old_news_item, 'created_at', datetime.now())
                    })
                    
                    if new_news:
                        news_migrated += 1
                        if news_migrated % 10 == 0:
                            print(f"   📊 تم تهجير {news_migrated} خبر...")
                    else:
                        print(f"   ❌ فشل في تهجير خبر: {old_news_item.title[:50]}...")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في تهجير الخبر: {str(e)}")
            
            print(f"   📊 تم تهجير {news_migrated} من {len(old_news)} خبر")
            
            # 3. تهجير التقارير المؤرشفة
            print("\n📋 تهجير التقارير المؤرشفة...")
            reports_migrated = 0
            
            for old_report in old_reports:
                try:
                    # ربط التصنيف الجديد
                    new_category_id = category_mapping.get(old_report.category_id, '') if old_report.category_id else ''
                    
                    new_report = ReportArchive.create({
                        'filename': old_report.filename,
                        'report_type': old_report.report_type,
                        'start_date': old_report.start_date,
                        'end_date': old_report.end_date,
                        'category_id': new_category_id,
                        'total_news': old_report.total_news,
                        'file_size': old_report.file_size,
                        'html_content': old_report.html_content,
                        'created_at': getattr(old_report, 'created_at', datetime.now())
                    })
                    
                    if new_report:
                        reports_migrated += 1
                        print(f"   ✅ {old_report.filename}")
                    else:
                        print(f"   ❌ فشل في تهجير: {old_report.filename}")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في تهجير التقرير {old_report.filename}: {str(e)}")
            
            print(f"   📊 تم تهجير {reports_migrated} من {len(old_reports)} تقرير")
            
            # ملخص التهجير
            print("\n🎉 ملخص التهجير:")
            print(f"   ✅ التصنيفات: {categories_migrated}/{len(old_categories)}")
            print(f"   ✅ الأخبار: {news_migrated}/{len(old_news)}")
            print(f"   ✅ التقارير: {reports_migrated}/{len(old_reports)}")
            
            total_old = len(old_categories) + len(old_news) + len(old_reports)
            total_migrated = categories_migrated + news_migrated + reports_migrated
            
            print(f"\n📊 إجمالي البيانات المهجرة: {total_migrated}/{total_old}")
            
            if total_migrated == total_old:
                print("🎉 تم تهجير جميع البيانات بنجاح!")
                return True
            else:
                print("⚠️ تم تهجير البيانات مع بعض الأخطاء.")
                return False
                
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        print("تأكد من تثبيت مكتبات Firebase:")
        print("pip install firebase-admin google-cloud-firestore")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام في التهجير: {str(e)}")
        return False

def verify_migration():
    """التحقق من نجاح التهجير"""
    print("\n🔍 التحقق من البيانات المهجرة...")
    
    try:
        from firebase_models import Category, News, ReportArchive
        
        # عد البيانات في Firebase
        categories = Category.get_all()
        news = News.get_all(limit=1000)  # حد أقصى للاختبار
        reports = ReportArchive.get_all()
        
        print(f"📊 البيانات في Firebase:")
        print(f"   - التصنيفات: {len(categories)}")
        print(f"   - الأخبار: {len(news)}")
        print(f"   - التقارير: {len(reports)}")
        
        # اختبار عينة من البيانات
        if categories:
            print(f"   ✅ عينة تصنيف: {categories[0].name}")
        
        if news:
            print(f"   ✅ عينة خبر: {news[0].title[:50]}...")
        
        if reports:
            print(f"   ✅ عينة تقرير: {reports[0].filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔄 تهجير البيانات من SQLite إلى Firebase Firestore")
    print("=" * 60)
    
    # التحقق من وجود ملف Service Account
    if not os.path.exists('firebase-service-account.json'):
        print("❌ ملف firebase-service-account.json غير موجود!")
        print("ضع ملف Service Account Key في مجلد التطبيق باسم 'firebase-service-account.json'")
        sys.exit(1)
    
    # بدء التهجير
    success = migrate_data()
    
    if success:
        # التحقق من النتائج
        verify_migration()
        print("\n✅ تم التهجير بنجاح! يمكنك الآن استخدام Firebase.")
    else:
        print("\n❌ فشل التهجير. راجع الأخطاء أعلاه.")
    
    print("=" * 60)
